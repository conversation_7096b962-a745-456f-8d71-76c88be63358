#!/bin/bash

# 📊 HTML Reports Viewer Script
# Quick access to all test reports

echo "📊 Playwright Framework - HTML Reports Viewer"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to open file based on OS
open_file() {
    local file="$1"
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        open "$file"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        xdg-open "$file"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        # Windows
        start "$file"
    else
        print_warning "Unknown OS. Please manually open: $file"
    fi
}

# Check what reports are available
print_info "Checking available reports..."
echo ""

# 1. TestNG/Surefire Reports
TESTNG_REPORT="target/surefire-reports/index.html"
if [ -f "$TESTNG_REPORT" ]; then
    print_success "✅ TestNG Report: $TESTNG_REPORT"
    TESTNG_AVAILABLE=true
else
    print_warning "❌ TestNG Report: Not found"
    TESTNG_AVAILABLE=false
fi

# 2. Allure Results
ALLURE_RESULTS="target/allure-results"
if [ -d "$ALLURE_RESULTS" ] && [ "$(ls -A $ALLURE_RESULTS)" ]; then
    ALLURE_COUNT=$(ls -1 "$ALLURE_RESULTS"/*.json 2>/dev/null | wc -l)
    print_success "✅ Allure Data: $ALLURE_COUNT result files"
    ALLURE_AVAILABLE=true
else
    print_warning "❌ Allure Data: Not found"
    ALLURE_AVAILABLE=false
fi

# 3. ExtentReports
EXTENT_DIR="test-results/reports"
if [ -d "$EXTENT_DIR" ]; then
    EXTENT_REPORTS=$(find "$EXTENT_DIR" -name "ExtentReport*.html" 2>/dev/null)
    if [ -n "$EXTENT_REPORTS" ]; then
        EXTENT_COUNT=$(echo "$EXTENT_REPORTS" | wc -l)
        print_success "✅ ExtentReports: $EXTENT_COUNT report(s) found"
        EXTENT_AVAILABLE=true
    else
        print_warning "❌ ExtentReports: Directory exists but no reports found"
        EXTENT_AVAILABLE=false
    fi
else
    print_warning "❌ ExtentReports: Directory not found"
    EXTENT_AVAILABLE=false
fi

# 4. Screenshots
SCREENSHOTS_DIR="test-results/screenshots"
if [ -d "$SCREENSHOTS_DIR" ]; then
    SCREENSHOT_COUNT=$(find "$SCREENSHOTS_DIR" -name "*.png" 2>/dev/null | wc -l)
    if [ "$SCREENSHOT_COUNT" -gt 0 ]; then
        print_success "✅ Screenshots: $SCREENSHOT_COUNT images found"
        SCREENSHOTS_AVAILABLE=true
    else
        print_warning "❌ Screenshots: Directory exists but no images found"
        SCREENSHOTS_AVAILABLE=false
    fi
else
    print_warning "❌ Screenshots: Directory not found"
    SCREENSHOTS_AVAILABLE=false
fi

echo ""
echo "📋 Available Actions:"
echo "===================="

# Show menu based on what's available
if [ "$TESTNG_AVAILABLE" = true ]; then
    echo "1. 📋 Open TestNG Report (Basic HTML)"
fi

if [ "$ALLURE_AVAILABLE" = true ]; then
    echo "2. 📈 Generate & Open Allure Report (Advanced)"
fi

if [ "$EXTENT_AVAILABLE" = true ]; then
    echo "3. 🎨 Open ExtentReports (Rich HTML)"
fi

if [ "$SCREENSHOTS_AVAILABLE" = true ]; then
    echo "4. 📸 Open Screenshots Directory"
fi

echo "5. 🚀 Run Test & Generate New Reports"
echo "6. 📊 Show Report Locations"
echo "7. ❌ Exit"

echo ""
read -p "Choose an option (1-7): " choice

case $choice in
    1)
        if [ "$TESTNG_AVAILABLE" = true ]; then
            print_info "Opening TestNG Report..."
            open_file "$TESTNG_REPORT"
            print_success "TestNG Report opened!"
        else
            print_error "TestNG Report not available. Run tests first."
        fi
        ;;
    2)
        if [ "$ALLURE_AVAILABLE" = true ]; then
            print_info "Generating Allure Report..."
            mvn allure:serve -s settings.xml
        else
            print_error "Allure data not available. Run tests first."
        fi
        ;;
    3)
        if [ "$EXTENT_AVAILABLE" = true ]; then
            print_info "Opening ExtentReports..."
            LATEST_EXTENT=$(find "$EXTENT_DIR" -name "ExtentReport*.html" | head -1)
            open_file "$LATEST_EXTENT"
            print_success "ExtentReport opened: $LATEST_EXTENT"
        else
            print_error "ExtentReports not available. Run tests first."
        fi
        ;;
    4)
        if [ "$SCREENSHOTS_AVAILABLE" = true ]; then
            print_info "Opening Screenshots Directory..."
            open_file "$SCREENSHOTS_DIR"
            print_success "Screenshots directory opened!"
        else
            print_error "Screenshots not available. Run tests first."
        fi
        ;;
    5)
        print_info "Running test and generating reports..."
        echo ""
        mvn test -Dtest=JeevansathiTests#simpleNavigationTest -s settings.xml -Dmaven.exec.skip=true -Dautomation.headless=true
        
        if [ $? -eq 0 ]; then
            print_success "Test completed! Opening reports..."
            sleep 2
            
            # Open TestNG report
            if [ -f "$TESTNG_REPORT" ]; then
                open_file "$TESTNG_REPORT"
            fi
            
            # Generate Allure report
            print_info "Generating Allure report..."
            mvn allure:serve -s settings.xml &
            
        else
            print_warning "Test execution had issues, but reports may still be available."
        fi
        ;;
    6)
        echo ""
        print_info "📁 Report Locations:"
        echo "===================="
        echo "📋 TestNG Report:     target/surefire-reports/index.html"
        echo "📈 Allure Data:       target/allure-results/"
        echo "🎨 ExtentReports:     test-results/reports/"
        echo "📸 Screenshots:       test-results/screenshots/"
        echo "📝 Logs:              test-results/logs/automation.log"
        echo ""
        echo "🚀 Commands:"
        echo "============"
        echo "• View TestNG:        open target/surefire-reports/index.html"
        echo "• Generate Allure:    mvn allure:serve -s settings.xml"
        echo "• Run Tests:          mvn test -Dtest=JeevansathiTests -s settings.xml -Dmaven.exec.skip=true"
        ;;
    7)
        print_info "Goodbye! 👋"
        exit 0
        ;;
    *)
        print_error "Invalid option. Please choose 1-7."
        ;;
esac

echo ""
print_info "Script completed. Run ./view-reports.sh again to access more reports."
