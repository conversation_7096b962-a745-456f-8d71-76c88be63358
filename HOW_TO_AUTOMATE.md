# 🚀 **How to Automate Using Your Playwright Framework**

## 🎯 **Quick Start - 3 Ways to Create Tests**

### **Method 1: AI-Generated Tests (Easiest!)**

#### **Option A: Write Prompts Directly in Code**
1. Open `src/test/java/com/automation/tests/MyCustomTests.java`
2. Add your test prompt in plain English:

```java
@Test(description = "AI Generated: Your test description")
public void testYourScenario() {
    String prompt = "Navigate to Amazon, search for iPhone, add to cart";
    String baseUrl = "https://amazon.com";
    executeAIGeneratedTest(prompt, baseUrl);
}
```

#### **Option B: Use External Prompt File**
1. Edit `test-prompts.txt` and add your prompts:
```
Navigate to Facebook, login with credentials, post a status update
Go to Netflix, search for action movies, add to watchlist
Visit LinkedIn, search for jobs, apply filters, save a job
```

2. Run the prompt-based tests:
```bash
mvn test -Dtest=PromptBasedTests -s settings.xml -Dmaven.exec.skip=true
```

#### **Option C: Command Line Generation**
```bash
# Generate and run a test from command line
./generate-test.sh "Login to Gmail and check inbox" "https://gmail.com"
./generate-test.sh "Search for laptops on Amazon" "https://amazon.com"
./generate-test.sh "Post a tweet on Twitter" "https://twitter.com"
```

### **Method 2: Manual Test Writing**
1. Create a new test class in `src/test/java/com/automation/tests/`
2. Extend `TestBase` class
3. Write your test methods using Playwright commands

### **Method 3: Hybrid Approach**
Combine AI generation with manual customization

---

## 📝 **What You Need to Provide**

### **For AI-Generated Tests:**
- **Plain English description** of what you want to test
- **Website URL** (optional, can be auto-detected)
- **OpenAI API Key** (optional, has fallback to rule-based generation)

### **Examples of Good Prompts:**
```
✅ "Navigate to Amazon, search for iPhone 15, filter by 4+ stars, add first item to cart"
✅ "Login to Facebook with username '<EMAIL>', create a post with text 'Hello World'"
✅ "Go to Netflix, search for 'action movies', play the first trailer"
✅ "Visit online banking, login, transfer $100 from checking to savings"
✅ "Open Gmail, compose email to '<EMAIL>', send with subject 'Test'"
```

### **For Manual Tests:**
- Basic Playwright knowledge (we provide examples)
- Element selectors (CSS, XPath)
- Test logic and assertions

---

## 🏃‍♂️ **Running Your Tests**

### **Run All Tests:**
```bash
mvn test -s settings.xml -Dmaven.exec.skip=true
```

### **Run Specific Test Class:**
```bash
# Run your custom AI tests
mvn test -Dtest=MyCustomTests -s settings.xml -Dmaven.exec.skip=true

# Run manual tests
mvn test -Dtest=MyManualTests -s settings.xml -Dmaven.exec.skip=true

# Run prompt-based tests
mvn test -Dtest=PromptBasedTests -s settings.xml -Dmaven.exec.skip=true
```

### **Run Specific Test Method:**
```bash
mvn test -Dtest=MyCustomTests#testGmailLogin -s settings.xml -Dmaven.exec.skip=true
```

### **Run with Different Browsers:**
```bash
# Chrome/Chromium (default)
mvn test -Dtest=MyCustomTests -s settings.xml -Dmaven.exec.skip=true -Dautomation.browser=chromium

# Firefox
mvn test -Dtest=MyCustomTests -s settings.xml -Dmaven.exec.skip=true -Dautomation.browser=firefox

# Safari/WebKit
mvn test -Dtest=MyCustomTests -s settings.xml -Dmaven.exec.skip=true -Dautomation.browser=webkit
```

### **Run in Headed Mode (See Browser):**
```bash
mvn test -Dtest=MyCustomTests -s settings.xml -Dmaven.exec.skip=true -Dautomation.headless=false
```

### **Run with Different Environments:**
```bash
# Development
mvn test -Dtest=MyCustomTests -s settings.xml -Dmaven.exec.skip=true -Denvironment=dev

# Staging
mvn test -Dtest=MyCustomTests -s settings.xml -Dmaven.exec.skip=true -Denvironment=staging

# Production
mvn test -Dtest=MyCustomTests -s settings.xml -Dmaven.exec.skip=true -Denvironment=prod
```

---

## 🔧 **Configuration**

### **Add OpenAI API Key (Optional):**
Edit `src/main/resources/config.properties`:
```properties
openai.api.key=your_openai_api_key_here
test.generation.enabled=true
```

### **Customize Browser Settings:**
```properties
browser=chromium
headless=false
default.timeout=30000
screenshot.on.failure=true
```

---

## 📊 **View Test Results**

### **Screenshots:**
- Location: `test-results/screenshots/`
- Automatically captured on test failure
- Optional capture on test success

### **Logs:**
- Location: `test-results/logs/automation.log`
- Detailed execution logs with timestamps

### **Reports:**
- **ExtentReports:** `test-results/reports/ExtentReport_*.html`
- **TestNG Reports:** `target/surefire-reports/`
- **Allure Reports:** Run `mvn allure:serve -s settings.xml`

---

## 💡 **Tips for Better Automation**

### **Writing Good Prompts:**
1. **Be Specific:** Include exact actions and expected results
2. **Use Real Websites:** Mention actual website names
3. **Include Data:** Specify what to search for, type, or select
4. **Add Verification:** Mention what should be verified

### **Manual Test Best Practices:**
1. **Use Page Object Model:** Create reusable page classes
2. **Add Assertions:** Verify expected outcomes
3. **Handle Waits:** Use explicit waits for dynamic content
4. **Take Screenshots:** Capture important moments

### **Debugging Tests:**
1. **Run in Headed Mode:** See what's happening in browser
2. **Add Logging:** Use `logTestInfo()`, `logTestPass()`, `logTestFail()`
3. **Check Screenshots:** Review captured images
4. **Review Logs:** Check detailed execution logs

---

## 🎯 **Real-World Examples**

### **E-commerce Testing:**
```java
String prompt = "Go to Amazon, search for 'wireless headphones', filter by Prime delivery, sort by price low to high, add first item to cart, proceed to checkout";
executeAIGeneratedTest(prompt, "https://amazon.com");
```

### **Social Media Testing:**
```java
String prompt = "Login to Twitter, search for hashtag #automation, like the first tweet, retweet with comment 'Great content!'";
executeAIGeneratedTest(prompt, "https://twitter.com");
```

### **Banking Testing:**
```java
String prompt = "Login to online banking, check account balance, transfer $50 from checking to savings, verify transaction appears in history";
executeAIGeneratedTest(prompt, "https://your-bank.com");
```

### **Travel Booking:**
```java
String prompt = "Search flights from NYC to LAX for next month, filter by non-stop, select cheapest option, enter passenger details";
executeAIGeneratedTest(prompt, "https://expedia.com");
```

---

## 🚀 **Getting Started Checklist**

- [ ] ✅ Framework is built and working
- [ ] ✅ Playwright browsers installed
- [ ] 📝 Choose your automation approach (AI vs Manual)
- [ ] 🎯 Write your first test prompt or manual test
- [ ] 🏃‍♂️ Run your test
- [ ] 📊 Check results and screenshots
- [ ] 🔄 Iterate and improve

**You're ready to start automating! Pick a method above and begin testing! 🎉**
