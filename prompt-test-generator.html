<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Prompt Test Generator - Playwright Framework</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #ffffff 0%, #fdf2f8 50%, #f3e8ff 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(216, 70, 239, 0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.2em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .form-container {
            padding: 40px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #a855f7;
            font-size: 1.1em;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #f3e8ff;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #d946ef;
            box-shadow: 0 0 0 3px rgba(216, 70, 239, 0.1);
        }
        
        textarea {
            resize: vertical;
            min-height: 120px;
        }
        
        .btn {
            background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }
        
        .btn:hover {
            background: linear-gradient(135deg, #c026d3 0%, #9333ea 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(216, 70, 239, 0.4);
        }
        
        .examples {
            background: #fdf2f8;
            border: 1px solid #f3e8ff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .examples h3 {
            margin-top: 0;
            color: #d946ef;
        }
        
        .example-item {
            background: white;
            border: 1px solid #e879f9;
            border-radius: 6px;
            padding: 10px;
            margin: 8px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .example-item:hover {
            background: #f3e8ff;
            transform: translateX(5px);
        }
        
        .output {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .hidden {
            display: none;
        }
        
        .success {
            color: #10b981;
            font-weight: bold;
        }
        
        .info {
            color: #3b82f6;
        }
        
        .warning {
            color: #f59e0b;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .form-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Prompt Test Generator</h1>
            <p>Create automated tests using natural language</p>
        </div>
        
        <div class="form-container">
            <div class="examples">
                <h3>💡 Example Prompts (Click to Use)</h3>
                <div class="example-item" onclick="useExample('Navigate to Jeevansathi, click login button, verify register button is displayed, click forgot password, check if OTP button appears')">
                    🔐 Login & Password Reset Flow
                </div>
                <div class="example-item" onclick="useExample('Search for laptop, filter by price range $500-1000, add first item to cart, proceed to checkout')">
                    🛒 E-commerce Shopping Flow
                </div>
                <div class="example-item" onclick="useExample('Fill registration form with name John Doe, email <EMAIL>, password test123, click submit, verify success message')">
                    📝 Registration Form Test
                </div>
                <div class="example-item" onclick="useExample('Navigate to contact page, fill contact form, submit, verify thank you message appears')">
                    📞 Contact Form Test
                </div>
            </div>
            
            <form id="promptForm">
                <div class="form-group">
                    <label for="prompt">🤖 Test Prompt (Describe what you want to test)</label>
                    <textarea id="prompt" name="prompt" placeholder="Example: Navigate to login page, enter username and password, click submit, verify dashboard appears" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="baseUrl">🌐 Base URL</label>
                    <input type="url" id="baseUrl" name="baseUrl" value="https://www.jeevansathi.com/" required>
                </div>
                
                <div class="form-group">
                    <label for="testName">📝 Test Name</label>
                    <input type="text" id="testName" name="testName" placeholder="My Custom Test" required>
                </div>
                
                <div class="form-group">
                    <label for="browser">🌐 Browser</label>
                    <select id="browser" name="browser">
                        <option value="chromium">Chromium</option>
                        <option value="firefox">Firefox</option>
                        <option value="webkit">WebKit</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="headless">👁️ Display Mode</label>
                    <select id="headless" name="headless">
                        <option value="true">Headless (Background)</option>
                        <option value="false">Visible (Show Browser)</option>
                    </select>
                </div>
                
                <button type="submit" class="btn">🚀 Generate & Run Test</button>
            </form>
            
            <div id="output" class="output hidden"></div>
        </div>
    </div>
    
    <script>
        function useExample(examplePrompt) {
            document.getElementById('prompt').value = examplePrompt;
            document.getElementById('testName').value = 'Generated from Example';
        }
        
        document.getElementById('promptForm').addEventListener('submit', function(e) {
            e.preventDefault();
            generateTest();
        });
        
        function generateTest() {
            const formData = new FormData(document.getElementById('promptForm'));
            const prompt = formData.get('prompt');
            const baseUrl = formData.get('baseUrl');
            const testName = formData.get('testName');
            const browser = formData.get('browser');
            const headless = formData.get('headless');
            
            const output = document.getElementById('output');
            output.classList.remove('hidden');
            
            // Generate Maven command
            const command = `mvn test -Dtest=PromptBasedTestRunner#userCustomTest \\
    -s settings.xml \\
    -Dmaven.exec.skip=true \\
    -Dautomation.headless=${headless} \\
    -Dautomation.browser=${browser}`;
            
            // Generate test file content
            const testFileContent = `// Generated Test: ${testName}
// Prompt: ${prompt}
// URL: ${baseUrl}

@Test(description = "${testName}")
public void ${testName.replace(/[^a-zA-Z0-9]/g, '')}() {
    String prompt = "${prompt}";
    String baseUrl = "${baseUrl}";
    executePromptBasedTest(prompt, baseUrl);
}`;
            
            output.innerHTML = `<span class="success">✅ Test Generated Successfully!</span>

<span class="info">📋 Test Details:</span>
Name: ${testName}
URL: ${baseUrl}
Browser: ${browser}
Mode: ${headless === 'true' ? 'Headless' : 'Visible'}

<span class="info">🤖 Your Prompt:</span>
${prompt}

<span class="info">🚀 To Run This Test:</span>

<span class="info">Option 1: Update PromptBasedTestRunner.java</span>
1. Open: src/test/java/com/automation/runners/PromptBasedTestRunner.java
2. In the userCustomTest() method, replace the prompt with:
   "${prompt}"
3. Run: ${command}

<span class="info">Option 2: Use Command Line Script</span>
./run-prompt-test.sh "${prompt}"

<span class="info">Option 3: Add to Prompts File</span>
Add this line to test-prompts.txt:
${testName} | ${baseUrl} | ${prompt}

Then run: mvn test -Dtest=FileBasedTestRunner#executeTestsFromFile -s settings.xml -Dmaven.exec.skip=true

<span class="info">📊 View Reports After Execution:</span>
open target/htmlReport/index.html

<span class="success">🎉 Your test is ready to run!</span>`;
        }
        
        // Auto-generate test name based on prompt
        document.getElementById('prompt').addEventListener('input', function() {
            const prompt = this.value;
            const testNameField = document.getElementById('testName');
            
            if (prompt && !testNameField.value) {
                let testName = prompt.substring(0, 50);
                testName = testName.replace(/[^a-zA-Z0-9\s]/g, '');
                testName = testName.trim();
                testName = testName.replace(/\s+/g, ' ');
                testName = testName + ' Test';
                testNameField.value = testName;
            }
        });
    </script>
</body>
</html>
