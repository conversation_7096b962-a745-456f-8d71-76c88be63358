# 📊 **HTML Test Reports - Complete Guide**

## 🎯 **Available HTML Reports**

Your Playwright framework generates **3 types of HTML reports**:

### **1. 📋 TestNG/Surefire Reports (Default)**
- **Location:** `target/surefire-reports/index.html`
- **Status:** ✅ **AVAILABLE NOW**
- **Features:** Basic test results, pass/fail status, execution time

### **2. 🎨 ExtentReports (Rich HTML)**
- **Location:** `test-results/reports/ExtentReport_[timestamp].html`
- **Status:** ⚠️ **NEEDS CONFIGURATION**
- **Features:** Rich HTML with screenshots, detailed logs, charts

### **3. 📈 Allure Reports (Advanced)**
- **Location:** Generated via `mvn allure:serve`
- **Status:** ✅ **DATA AVAILABLE** (needs generation)
- **Features:** Advanced analytics, trends, detailed test breakdown

---

## 🚀 **How to View Each Report**

### **📋 Method 1: TestNG/Surefire Reports (Immediate)**

```bash
# Open the basic HTML report
open target/surefire-reports/index.html

# Or on Linux/Windows
xdg-open target/surefire-reports/index.html  # Linux
start target/surefire-reports/index.html     # Windows
```

**What you'll see:**
- ✅ Test execution summary
- ✅ Pass/Fail status for each test
- ✅ Execution time
- ✅ Basic error messages

### **🎨 Method 2: ExtentReports (Rich HTML)**

First, let's configure ExtentReports properly:

```bash
# Run tests to generate ExtentReports
mvn test -Dtest=JeevansathiTests#simpleNavigationTest -s settings.xml -Dmaven.exec.skip=true

# The report will be generated at:
# test-results/reports/ExtentReport_[timestamp].html
```

**What you'll see:**
- 🎨 Beautiful HTML interface
- 📸 Screenshots embedded in report
- 📊 Test execution charts and graphs
- 📝 Detailed step-by-step logs
- 🏷️ Test categories and tags

### **📈 Method 3: Allure Reports (Most Advanced)**

```bash
# Generate and serve Allure report
mvn allure:serve -s settings.xml

# This will:
# 1. Generate HTML report from allure-results
# 2. Start local server
# 3. Open browser automatically
```

**What you'll see:**
- 📈 Advanced test analytics
- 📊 Execution trends over time
- 🔍 Detailed test breakdown
- 📸 Screenshots and attachments
- 📋 Test categories and suites
- ⏱️ Performance metrics

---

## 🔧 **Quick Commands to Generate Reports**

### **Generate All Reports:**
```bash
# Run a test and generate all reports
mvn test -Dtest=JeevansathiTests#simpleNavigationTest -s settings.xml -Dmaven.exec.skip=true

# View TestNG report
open target/surefire-reports/index.html

# Generate and view Allure report
mvn allure:serve -s settings.xml
```

### **Run Full Test Suite and Generate Reports:**
```bash
# Run all Jeevansathi tests
mvn test -Dtest=JeevansathiTests -s settings.xml -Dmaven.exec.skip=true

# View all reports
open target/surefire-reports/index.html  # Basic report
mvn allure:serve -s settings.xml         # Advanced report
```

---

## 📁 **Report File Locations**

```
📁 Project Root
├── 📁 target/
│   ├── 📁 surefire-reports/
│   │   ├── 📄 index.html              ← **MAIN TESTNG REPORT**
│   │   ├── 📄 Surefire suite/
│   │   │   └── 📄 Surefire test.html  ← **DETAILED TEST REPORT**
│   │   └── 📄 *.xml                   ← **XML TEST RESULTS**
│   └── 📁 allure-results/
│       └── 📄 *.json                  ← **ALLURE DATA FILES**
├── 📁 test-results/
│   ├── 📁 reports/
│   │   └── 📄 ExtentReport_*.html     ← **EXTENTREPORTS**
│   ├── 📁 screenshots/
│   │   └── 📄 *.png                   ← **TEST SCREENSHOTS**
│   └── 📁 logs/
│       └── 📄 automation.log          ← **DETAILED LOGS**
```

---

## 🎯 **Current Status**

### ✅ **Working Now:**
1. **TestNG Reports:** `target/surefire-reports/index.html`
2. **Allure Data:** Available in `target/allure-results/`

### ⚙️ **Needs Setup:**
1. **ExtentReports:** Need to run tests to generate
2. **Allure HTML:** Need to run `mvn allure:serve`

---

## 🚀 **Quick Start - View Reports Now**

### **Step 1: View Basic Report (Available Now)**
```bash
open target/surefire-reports/index.html
```

### **Step 2: Generate Advanced Report**
```bash
mvn allure:serve -s settings.xml
```

### **Step 3: Run New Test and See All Reports**
```bash
# Run test
mvn test -Dtest=JeevansathiTests#simpleNavigationTest -s settings.xml -Dmaven.exec.skip=true

# View reports
open target/surefire-reports/index.html  # Basic
mvn allure:serve -s settings.xml         # Advanced
```

---

## 📸 **Screenshots in Reports**

Your framework automatically captures screenshots:
- ✅ **On test failure**
- ✅ **On test success** (configurable)
- ✅ **At key test steps**

Screenshots are embedded in:
- 🎨 **ExtentReports:** Directly embedded in HTML
- 📈 **Allure Reports:** As attachments
- 📋 **TestNG Reports:** Links to screenshot files

---

## 🔍 **What Each Report Shows**

### **TestNG/Surefire (Basic):**
- Test execution summary
- Pass/fail counts
- Execution time
- Basic error messages

### **ExtentReports (Rich):**
- Beautiful HTML interface
- Screenshots embedded
- Step-by-step logs
- Charts and graphs
- Test categories

### **Allure (Advanced):**
- Test execution trends
- Performance metrics
- Detailed test breakdown
- Historical data
- Advanced analytics

---

## 💡 **Pro Tips**

1. **Quick View:** Always start with `target/surefire-reports/index.html`
2. **Detailed Analysis:** Use `mvn allure:serve` for comprehensive reports
3. **Screenshots:** Check `test-results/screenshots/` for all captured images
4. **Logs:** Review `test-results/logs/automation.log` for detailed execution logs
5. **CI/CD:** TestNG reports work great in CI/CD pipelines

---

**🎉 Your HTML reports are ready! Start with the TestNG report and then explore Allure for advanced features.**
