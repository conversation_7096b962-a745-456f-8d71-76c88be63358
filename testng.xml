<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="Playwright Automation Framework Suite" verbose="1" parallel="false" thread-count="1">

    <!-- Global Parameters -->
    <parameter name="browser" value="chromium"/>
    <parameter name="headless" value="false"/>
    <parameter name="environment" value="dev"/>
    <parameter name="timeout" value="30000"/>
    <parameter name="retryCount" value="1"/>

    <!-- Listeners for Enhanced Reporting -->
    <listeners>
        <listener class-name="com.automation.listeners.TestListener"/>
        <listener class-name="com.automation.listeners.AllureListener"/>
        <listener class-name="com.automation.listeners.ExtentReportListener"/>
    </listeners>

    <!-- Smoke Tests - Critical Path -->
    <test name="Smoke Tests" preserve-order="true" enabled="true">
        <parameter name="testType" value="smoke"/>
        <groups>
            <run>
                <include name="smoke"/>
                <include name="navigation"/>
            </run>
        </groups>
        <classes>
            <class name="com.automation.logic.JeevansathiLogic">
                <methods>
                    <include name="simpleNavigationTest"/>
                </methods>
            </class>
        </classes>
    </test>

    <!-- Functional Tests - Core Features -->
    <test name="Functional Tests" preserve-order="true" enabled="true">
        <parameter name="testType" value="functional"/>
        <groups>
            <run>
                <include name="jeevansathi"/>
                <include name="password-reset"/>
                <include name="verification"/>
            </run>
        </groups>
        <classes>
            <class name="com.automation.logic.JeevansathiLogic">
                <methods>
                    <include name="resetPasswordTest"/>
                    <include name="verifyPageElementsTest"/>
                </methods>
            </class>
        </classes>
    </test>

    <!-- Advanced Tests - AI and Integration -->
    <test name="Advanced Tests" preserve-order="true" enabled="true">
        <parameter name="testType" value="advanced"/>
        <groups>
            <run>
                <include name="prompt-based"/>
                <include name="augment"/>
            </run>
        </groups>
        <classes>
            <class name="com.automation.logic.JeevansathiLogic">
                <methods>
                    <include name="promptBasedTest"/>
                    <include name="augmentIntegrationDemo"/>
                </methods>
            </class>
        </classes>
    </test>

    <!-- Complete Suite - All Tests (Disabled by default) -->
    <test name="Complete Test Suite" preserve-order="true" enabled="false">
        <parameter name="testType" value="complete"/>
        <classes>
            <class name="com.automation.logic.JeevansathiLogic">
                <methods>
                    <include name="simpleNavigationTest"/>
                    <include name="resetPasswordTest"/>
                    <include name="verifyPageElementsTest"/>
                    <include name="promptBasedTest"/>
                    <include name="augmentIntegrationDemo"/>
                </methods>
            </class>
        </classes>
    </test>

</suite>
