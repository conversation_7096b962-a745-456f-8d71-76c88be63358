<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="PlaywrightAutomationSuite" verbose="1" parallel="methods" thread-count="3">
    
    <parameter name="browser" value="chromium"/>
    <parameter name="headless" value="false"/>
    <parameter name="environment" value="dev"/>
    
    <listeners>
        <listener class-name="com.automation.listeners.TestListener"/>
        <listener class-name="com.automation.listeners.AllureListener"/>
        <listener class-name="com.automation.listeners.ExtentReportListener"/>
    </listeners>
    
    <!-- Test Execution from Logic Package -->
    <test name="Jeevansathi Tests" preserve-order="true">
        <parameter name="testType" value="jeevansathi"/>
        <classes>
            <class name="com.automation.logic.JeevansathiLogic">
                <methods>
                    <include name="resetPasswordTest"/>
                    <include name="simpleNavigationTest"/>
                    <include name="verifyPageElementsTest"/>
                    <include name="promptBasedTest"/>
                    <include name="augmentIntegrationDemo"/>
                </methods>
            </class>
        </classes>
    </test>
    
</suite>
