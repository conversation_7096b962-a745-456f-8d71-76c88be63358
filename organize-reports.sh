#!/bin/bash

# 📊 HTML Reports Organizer
# Moves all reports to target/htmlReport directory post-execution

echo "📊 Organizing HTML Reports to target/htmlReport"
echo "==============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create the centralized directory structure
print_info "Creating centralized report directory structure..."
mkdir -p target/htmlReport/{testng,allure,allure-results,extent,screenshots,logs}

# Function to move files safely
move_files() {
    local source="$1"
    local destination="$2"
    local description="$3"
    
    if [ -d "$source" ] || [ -f "$source" ]; then
        print_info "Moving $description..."
        cp -r "$source"/* "$destination"/ 2>/dev/null || cp "$source" "$destination"/ 2>/dev/null
        if [ $? -eq 0 ]; then
            print_success "✅ $description moved to $destination"
        else
            print_warning "⚠️ Could not move $description"
        fi
    else
        print_info "📝 $description not found (may not have been generated yet)"
    fi
}

# 1. Move TestNG/Surefire Reports (if they exist in old location)
if [ -d "target/surefire-reports" ] && [ "$(ls -A target/surefire-reports 2>/dev/null)" ]; then
    move_files "target/surefire-reports" "target/htmlReport/testng" "TestNG/Surefire Reports"
fi

# 2. Move Allure Results (if they exist in old location)
if [ -d "target/allure-results" ] && [ "$(ls -A target/allure-results 2>/dev/null)" ]; then
    move_files "target/allure-results" "target/htmlReport/allure-results" "Allure Results"
fi

# 3. Move ExtentReports (if they exist in old location)
if [ -d "test-results/reports" ] && [ "$(ls -A test-results/reports 2>/dev/null)" ]; then
    move_files "test-results/reports" "target/htmlReport/extent" "ExtentReports"
fi

# 4. Move Screenshots (if they exist in old location)
if [ -d "test-results/screenshots" ] && [ "$(ls -A test-results/screenshots 2>/dev/null)" ]; then
    move_files "test-results/screenshots" "target/htmlReport/screenshots" "Screenshots"
fi

# 5. Move Logs (if they exist in old location)
if [ -d "test-results/logs" ] && [ "$(ls -A test-results/logs 2>/dev/null)" ]; then
    move_files "test-results/logs" "target/htmlReport/logs" "Log Files"
fi

# 6. Create index.html for easy navigation
print_info "Creating navigation index..."
cat > target/htmlReport/index.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Playwright Automation Framework - Test Reports</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.8;
            font-size: 1.1em;
        }
        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            padding: 40px;
        }
        .report-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .report-header {
            padding: 20px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }
        .report-header.testng { background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); }
        .report-header.allure { background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); }
        .report-header.extent { background: linear-gradient(135deg, #27ae60 0%, #229954 100%); }
        .report-header.screenshots { background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%); }
        .report-header h3 {
            margin: 0;
            font-size: 1.4em;
        }
        .report-header .icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        .report-content {
            padding: 20px;
        }
        .report-content p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s ease;
            font-weight: 500;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn.disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.available { background: #d5f4e6; color: #27ae60; }
        .status.pending { background: #fef9e7; color: #f39c12; }
        .footer {
            text-align: center;
            padding: 20px;
            background: #ecf0f1;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Test Reports Dashboard</h1>
            <p>Playwright Automation Framework - Centralized HTML Reports</p>
        </div>
        
        <div class="reports-grid">
            <div class="report-card">
                <div class="report-header testng">
                    <div class="icon">📋</div>
                    <h3>TestNG Reports</h3>
                </div>
                <div class="report-content">
                    <p>Basic test execution reports with pass/fail status, execution time, and error details.</p>
                    <a href="testng/index.html" class="btn" id="testng-btn">View TestNG Report</a>
                    <span class="status" id="testng-status">Checking...</span>
                </div>
            </div>
            
            <div class="report-card">
                <div class="report-header allure">
                    <div class="icon">📈</div>
                    <h3>Allure Reports</h3>
                </div>
                <div class="report-content">
                    <p>Advanced analytics with trends, detailed test breakdown, and performance metrics.</p>
                    <a href="#" class="btn" onclick="generateAllure()">Generate Allure Report</a>
                    <span class="status" id="allure-status">Checking...</span>
                </div>
            </div>
            
            <div class="report-card">
                <div class="report-header extent">
                    <div class="icon">🎨</div>
                    <h3>ExtentReports</h3>
                </div>
                <div class="report-content">
                    <p>Rich HTML reports with embedded screenshots, detailed logs, and beautiful charts.</p>
                    <a href="#" class="btn" id="extent-btn">View ExtentReport</a>
                    <span class="status" id="extent-status">Checking...</span>
                </div>
            </div>
            
            <div class="report-card">
                <div class="report-header screenshots">
                    <div class="icon">📸</div>
                    <h3>Screenshots</h3>
                </div>
                <div class="report-content">
                    <p>Test execution screenshots captured during test runs for visual verification.</p>
                    <a href="screenshots/" class="btn" id="screenshots-btn">View Screenshots</a>
                    <span class="status" id="screenshots-status">Checking...</span>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>Generated by Playwright Automation Framework | Last Updated: <span id="timestamp"></span></p>
        </div>
    </div>
    
    <script>
        // Set timestamp
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
        
        // Check report availability
        function checkReportAvailability() {
            // This would need to be implemented with actual file checking
            // For now, we'll assume reports might be available
            
            // Check TestNG
            fetch('testng/index.html')
                .then(response => {
                    if (response.ok) {
                        document.getElementById('testng-status').textContent = 'Available';
                        document.getElementById('testng-status').className = 'status available';
                    } else {
                        document.getElementById('testng-status').textContent = 'Not Generated';
                        document.getElementById('testng-status').className = 'status pending';
                        document.getElementById('testng-btn').className = 'btn disabled';
                    }
                })
                .catch(() => {
                    document.getElementById('testng-status').textContent = 'Not Generated';
                    document.getElementById('testng-status').className = 'status pending';
                    document.getElementById('testng-btn').className = 'btn disabled';
                });
        }
        
        function generateAllure() {
            alert('Run: mvn allure:serve -s settings.xml');
        }
        
        // Check availability on load
        checkReportAvailability();
    </script>
</body>
</html>
EOF

print_success "✅ Navigation index created at target/htmlReport/index.html"

# 7. Show summary
echo ""
print_info "📊 Report Organization Summary:"
echo "==============================="
echo "📁 Main Directory:     target/htmlReport/"
echo "📋 TestNG Reports:     target/htmlReport/testng/"
echo "📈 Allure Results:     target/htmlReport/allure-results/"
echo "📈 Allure Reports:     target/htmlReport/allure/"
echo "🎨 ExtentReports:      target/htmlReport/extent/"
echo "📸 Screenshots:       target/htmlReport/screenshots/"
echo "📝 Logs:              target/htmlReport/logs/"
echo "🏠 Navigation:         target/htmlReport/index.html"

echo ""
print_success "🎉 All reports organized in target/htmlReport directory!"
print_info "💡 Open target/htmlReport/index.html for easy navigation"
