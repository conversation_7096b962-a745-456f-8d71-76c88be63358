#!/bin/bash

# Playwright Automation Framework Test Execution Script
# This script provides various options for running tests

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
BROWSER="chromium"
HEADLESS="false"
ENVIRONMENT="dev"
TEST_SUITE="smoke"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -b, --browser BROWSER      Browser to use (chromium, firefox, webkit) [default: chromium]"
    echo "  -h, --headless             Run in headless mode [default: false]"
    echo "  -e, --environment ENV      Environment to test (dev, staging, prod) [default: dev]"
    echo "  -s, --suite SUITE          Test suite to run (smoke, all) [default: smoke]"
    echo "  --help                     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run smoke tests with default settings"
    echo "  $0 -b firefox -h                     # Run in Firefox headless mode"
    echo "  $0 -s smoke -e staging               # Run smoke tests in staging environment"
}

# Function to install Playwright browsers
install_browsers() {
    print_info "Installing Playwright browsers..."
    mvn exec:java -Dexec.mainClass="com.microsoft.playwright.CLI" -Dexec.args="install" -q
    if [ $? -eq 0 ]; then
        print_success "Playwright browsers installed successfully"
    else
        print_error "Failed to install Playwright browsers"
        exit 1
    fi
}

# Function to validate Java and Maven
validate_prerequisites() {
    print_info "Validating prerequisites..."
    
    # Check Java
    if ! command -v java &> /dev/null; then
        print_error "Java is not installed or not in PATH"
        exit 1
    fi
    
    # Check Maven
    if ! command -v mvn &> /dev/null; then
        print_error "Maven is not installed or not in PATH"
        exit 1
    fi
    
    print_success "Prerequisites validated"
}

# Function to run tests
run_tests() {
    print_info "Starting test execution..."
    print_info "Configuration:"
    print_info "  Browser: $BROWSER"
    print_info "  Headless: $HEADLESS"
    print_info "  Environment: $ENVIRONMENT"
    print_info "  Test Suite: $TEST_SUITE"
    
    # Build Maven command
    MVN_CMD="mvn test"
    MVN_CMD="$MVN_CMD -Dautomation.browser=$BROWSER"
    MVN_CMD="$MVN_CMD -Dautomation.headless=$HEADLESS"
    MVN_CMD="$MVN_CMD -Denvironment=$ENVIRONMENT"
    
    # Set test suite
    case $TEST_SUITE in
        "smoke")
            MVN_CMD="$MVN_CMD -Dtest=SmokeTests"
            ;;
        "all")
            # Run all tests - no specific test filter
            ;;
        *)
            print_error "Invalid test suite: $TEST_SUITE"
            exit 1
            ;;
    esac
    
    # Execute tests
    print_info "Executing command: $MVN_CMD"
    eval $MVN_CMD
    
    TEST_EXIT_CODE=$?
    
    if [ $TEST_EXIT_CODE -eq 0 ]; then
        print_success "All tests passed!"
    else
        print_warning "Some tests failed or were skipped (exit code: $TEST_EXIT_CODE)"
    fi
    
    return $TEST_EXIT_CODE
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -b|--browser)
            BROWSER="$2"
            shift 2
            ;;
        -h|--headless)
            HEADLESS="true"
            shift
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -s|--suite)
            TEST_SUITE="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_info "Playwright Automation Framework Test Runner"
    print_info "============================================"
    
    # Validate prerequisites
    validate_prerequisites
    
    # Install browsers
    install_browsers
    
    # Run tests
    run_tests
    TEST_RESULT=$?
    
    # Exit with test result code
    if [ $TEST_RESULT -eq 0 ]; then
        print_success "Test execution completed successfully!"
    else
        print_warning "Test execution completed with failures"
    fi
    
    exit $TEST_RESULT
}

# Run main function
main
