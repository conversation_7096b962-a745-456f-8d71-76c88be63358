#!/bin/bash

# Advanced Tests Runner
# Runs AI-powered and integration tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse command line arguments
HEADLESS="false"
BROWSER="chromium"

while [[ $# -gt 0 ]]; do
    case $1 in
        --headless)
            HEADLESS="true"
            shift
            ;;
        --headed)
            HEADLESS="false"
            shift
            ;;
        --browser)
            BROWSER="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --headless    Run tests in headless mode (default: false)"
            echo "  --headed      Run tests with visible browser"
            echo "  --browser     Browser to use (chromium, firefox, webkit)"
            echo "  --help        Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    # Run with visible browser"
            echo "  $0 --headless        # Run in headless mode"
            echo "  $0 --browser firefox  # Run with Firefox"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo ""
echo "🤖 Playwright Automation Framework - Advanced Tests"
echo "===================================================="
print_info "Browser: $BROWSER"
print_info "Headless: $HEADLESS"
print_info "Test Type: Advanced Tests (AI & Integration)"
echo ""

# Clean previous reports
print_info "Cleaning previous test reports..."
rm -rf target/htmlReport/* 2>/dev/null || true
rm -rf target/surefire-reports/* 2>/dev/null || true
rm -rf target/allure-results/* 2>/dev/null || true

# Run advanced tests
print_info "Running Advanced Tests..."
echo ""

mvn test \
    -Dtest="Advanced Tests" \
    -s settings.xml \
    -Dmaven.exec.skip=true \
    -Dautomation.headless=$HEADLESS \
    -Dautomation.browser=$BROWSER \
    -Dtestng.groups="advanced,prompt-based,augment"

TEST_EXIT_CODE=$?

echo ""
if [ $TEST_EXIT_CODE -eq 0 ]; then
    print_success "✅ Advanced Tests completed successfully!"
else
    print_error "❌ Advanced Tests failed with exit code: $TEST_EXIT_CODE"
fi

# Organize reports
print_info "Organizing test reports..."
./organize-reports.sh

# Display results
echo ""
print_info "📊 Test Results Summary:"
echo "========================="

if [ -f "target/surefire-reports/testng-results.xml" ]; then
    TOTAL=$(grep -o 'total="[0-9]*"' target/surefire-reports/testng-results.xml | grep -o '[0-9]*' || echo "0")
    PASSED=$(grep -o 'passed="[0-9]*"' target/surefire-reports/testng-results.xml | grep -o '[0-9]*' || echo "0")
    FAILED=$(grep -o 'failed="[0-9]*"' target/surefire-reports/testng-results.xml | grep -o '[0-9]*' || echo "0")
    SKIPPED=$(grep -o 'skipped="[0-9]*"' target/surefire-reports/testng-results.xml | grep -o '[0-9]*' || echo "0")
    
    echo "📈 Total Tests: $TOTAL"
    echo "✅ Passed: $PASSED"
    echo "❌ Failed: $FAILED"
    echo "⏭️  Skipped: $SKIPPED"
else
    print_warning "Test results not found"
fi

echo ""
print_info "📋 Available Reports:"
echo "  📊 Main Report:     target/htmlReport/index.html"
echo "  📈 TestNG Report:   target/htmlReport/testng/index.html"
echo "  📸 Screenshots:    target/htmlReport/screenshots/"
echo "  📝 Logs:           target/htmlReport/logs/"

echo ""
print_info "🤖 Advanced Features Tested:"
echo "  🎯 Prompt-Based Test Generation"
echo "  🔗 Augment Integration (with fallbacks)"
echo "  🧠 AI-Powered Test Creation"
echo "  📊 Intelligent Reporting"

echo ""
print_info "🚀 Quick Commands:"
echo "  open target/htmlReport/index.html    # View main report"
echo "  ./run-smoke-tests.sh                 # Run smoke tests"
echo "  ./run-functional-tests.sh            # Run functional tests"
echo "  ./run-complete-suite.sh              # Run complete suite"

echo ""
if [ $TEST_EXIT_CODE -eq 0 ]; then
    print_success "🎉 Advanced Tests Suite completed successfully!"
    exit 0
else
    print_error "💥 Advanced Tests Suite failed!"
    exit $TEST_EXIT_CODE
fi
