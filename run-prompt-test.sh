#!/bin/bash

# 🤖 Command Line Prompt Test Runner
# Users can provide prompts directly via command line

echo "🚀 Playwright Framework - Prompt-Based Test Runner"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_prompt() {
    echo -e "${MAGENTA}[PROMPT]${NC} $1"
}

# Function to get user input
get_user_input() {
    local prompt_text="$1"
    local default_value="$2"
    local user_input
    
    if [ -n "$default_value" ]; then
        read -p "$prompt_text [$default_value]: " user_input
        echo "${user_input:-$default_value}"
    else
        read -p "$prompt_text: " user_input
        echo "$user_input"
    fi
}

# Main function
main() {
    print_info "Welcome to the Prompt-Based Test Runner!"
    echo ""
    
    # Check if prompt provided as command line argument
    if [ $# -gt 0 ]; then
        USER_PROMPT="$*"
        print_info "Using command line prompt: $USER_PROMPT"
    else
        # Interactive mode
        print_info "🎯 Interactive Mode - Create your test with natural language"
        echo ""
        echo "Examples of good prompts:"
        echo "  • Navigate to login page, enter credentials, verify dashboard"
        echo "  • Search for 'laptop', filter by price, add to cart, checkout"
        echo "  • Fill registration form, submit, verify success message"
        echo "  • Click forgot password, enter email, verify OTP page"
        echo ""
        
        USER_PROMPT=$(get_user_input "🤖 Enter your test prompt")
        
        if [ -z "$USER_PROMPT" ]; then
            print_error "No prompt provided. Exiting."
            exit 1
        fi
    fi
    
    # Get base URL
    echo ""
    BASE_URL=$(get_user_input "🌐 Enter base URL" "https://www.jeevansathi.com/")
    
    # Get test options
    echo ""
    print_info "🔧 Test Options:"
    HEADLESS=$(get_user_input "Run in headless mode? (true/false)" "true")
    BROWSER=$(get_user_input "Browser (chromium/firefox/webkit)" "chromium")
    
    # Display test summary
    echo ""
    print_info "📋 Test Summary:"
    echo "  🤖 Prompt: $USER_PROMPT"
    echo "  🌐 URL: $BASE_URL"
    echo "  👁️  Headless: $HEADLESS"
    echo "  🌐 Browser: $BROWSER"
    echo ""
    
    # Confirm execution
    CONFIRM=$(get_user_input "Execute this test? (y/n)" "y")
    
    if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
        print_info "Test execution cancelled."
        exit 0
    fi
    
    # Create temporary test file
    create_temp_test_file "$USER_PROMPT" "$BASE_URL"
    
    # Execute the test
    execute_prompt_test "$HEADLESS" "$BROWSER"
    
    # Cleanup
    cleanup_temp_files
}

# Create temporary test file with user prompt
create_temp_test_file() {
    local prompt="$1"
    local url="$2"
    
    print_info "📝 Creating temporary test file..."
    
    cat > temp-prompt-test.java << EOF
package com.automation.runners;

import com.automation.core.TestBase;
import com.automation.generator.TestGenerator;
import com.automation.models.TestCase;
import com.automation.models.TestStep;
import org.testng.annotations.Test;
import java.util.List;

public class TempPromptTest extends TestBase {
    
    private final TestGenerator testGenerator = new TestGenerator();
    
    @Test(description = "User Generated Prompt Test")
    public void userPromptTest() {
        String prompt = "$prompt";
        String baseUrl = "$url";
        
        logTestInfo("🤖 Executing user prompt: " + prompt);
        logTestInfo("🌐 Base URL: " + baseUrl);
        
        try {
            List<TestCase> testCases = testGenerator.generateTestCases(prompt, baseUrl);
            
            if (testCases.isEmpty()) {
                logTestFail("❌ No test cases generated");
                return;
            }
            
            TestCase testCase = testCases.get(0);
            logTestInfo("✅ Generated " + testCase.getSteps().size() + " test steps");
            
            for (TestStep step : testCase.getSteps()) {
                logTestInfo("📋 " + step.getDescription());
                executeStep(step);
            }
            
            logTestPass("🎉 User prompt test completed successfully!");
            
        } catch (Exception e) {
            logTestFail("❌ Test failed: " + e.getMessage());
            takeScreenshot("User prompt test failure");
        }
    }
    
    private void executeStep(TestStep step) {
        try {
            switch (step.getAction().toUpperCase()) {
                case "NAVIGATE":
                    if (step.getInputData() != null) {
                        navigateTo(step.getInputData());
                        getPage().waitForTimeout(2000);
                    }
                    break;
                case "CLICK":
                    if (step.getLocator() != null) {
                        getPage().click(step.getLocator());
                        getPage().waitForTimeout(1000);
                    }
                    break;
                case "TYPE":
                case "FILL":
                    if (step.getLocator() != null && step.getInputData() != null) {
                        getPage().fill(step.getLocator(), step.getInputData());
                    }
                    break;
                case "VERIFY":
                    if (step.getLocator() != null) {
                        boolean isVisible = getPage().locator(step.getLocator()).isVisible();
                        if (isVisible) {
                            logTestPass("✅ Verification passed: " + step.getDescription());
                        } else {
                            logTestInfo("⚠️ Verification failed: " + step.getDescription());
                        }
                    }
                    break;
                case "WAIT":
                    getPage().waitForTimeout(2000);
                    break;
            }
        } catch (Exception e) {
            logTestInfo("⚠️ Step issue: " + e.getMessage());
        }
    }
}
EOF

    print_success "✅ Temporary test file created"
}

# Execute the prompt test
execute_prompt_test() {
    local headless="$1"
    local browser="$2"
    
    print_info "🚀 Executing prompt-based test..."
    echo ""
    
    # Compile the temporary test
    print_info "🔧 Compiling test..."
    mvn test-compile -s settings.xml -Dmaven.exec.skip=true -q
    
    if [ $? -ne 0 ]; then
        print_error "❌ Compilation failed"
        return 1
    fi
    
    # Run the test
    print_info "🎯 Running test..."
    mvn test -Dtest=TempPromptTest \
        -s settings.xml \
        -Dmaven.exec.skip=true \
        -Dautomation.headless="$headless" \
        -Dautomation.browser="$browser"
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        print_success "🎉 Test completed successfully!"
    else
        print_warning "⚠️ Test completed with issues (exit code: $exit_code)"
    fi
    
    # Organize reports
    print_info "📊 Organizing reports..."
    ./organize-reports.sh > /dev/null 2>&1
    
    # Show report locations
    echo ""
    print_info "📋 Test Reports:"
    echo "  📊 Main Dashboard: open target/htmlReport/index.html"
    echo "  📋 TestNG Report: open target/htmlReport/testng/index.html"
    echo "  📈 Generate Allure: mvn allure:serve -s settings.xml"
    
    return $exit_code
}

# Cleanup temporary files
cleanup_temp_files() {
    print_info "🧹 Cleaning up temporary files..."
    rm -f temp-prompt-test.java
    print_success "✅ Cleanup completed"
}

# Handle script interruption
trap cleanup_temp_files EXIT

# Show usage if help requested
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    echo "Usage: $0 [prompt]"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Interactive mode"
    echo "  $0 \"Navigate to login and verify\"    # Direct prompt"
    echo "  $0 \"Search for laptop and add to cart\" # E-commerce test"
    echo ""
    echo "The script will prompt for URL and test options."
    exit 0
fi

# Run main function
main "$@"
