#!/bin/bash

# 🤖 AI Test Generator Script
# Usage: ./generate-test.sh "Your test prompt here" "https://website.com"

echo "🤖 AI Test Generator for Playwright Framework"
echo "=============================================="

# Check if prompt is provided
if [ -z "$1" ]; then
    echo "❌ Error: Please provide a test prompt"
    echo ""
    echo "📋 Usage:"
    echo "  ./generate-test.sh \"Your test description\" \"https://website.com\""
    echo ""
    echo "📝 Examples:"
    echo "  ./generate-test.sh \"Login to Gmail and check inbox\" \"https://gmail.com\""
    echo "  ./generate-test.sh \"Search for iPhone on Amazon\" \"https://amazon.com\""
    echo "  ./generate-test.sh \"Post a tweet on Twitter\" \"https://twitter.com\""
    echo ""
    exit 1
fi

PROMPT="$1"
BASE_URL="${2:-https://example.com}"

echo "🎯 Test Prompt: $PROMPT"
echo "🌐 Base URL: $BASE_URL"
echo ""

# Create a temporary test class
TEST_CLASS_NAME="GeneratedTest_$(date +%s)"
TEST_FILE="src/test/java/com/automation/tests/generated/${TEST_CLASS_NAME}.java"

# Create the generated directory if it doesn't exist
mkdir -p "src/test/java/com/automation/tests/generated"

# Generate the test class
cat > "$TEST_FILE" << EOF
package com.automation.tests.generated;

import com.automation.core.TestBase;
import com.automation.generator.TestGenerator;
import com.automation.models.TestCase;
import org.testng.annotations.Test;

import java.util.List;

/**
 * Auto-generated test from command line prompt
 * Generated on: $(date)
 * Prompt: $PROMPT
 * Base URL: $BASE_URL
 */
public class $TEST_CLASS_NAME extends TestBase {
    
    private final TestGenerator testGenerator = new TestGenerator();
    
    @Test(description = "AI Generated: $PROMPT")
    public void testGeneratedFromPrompt() {
        String prompt = "$PROMPT";
        String baseUrl = "$BASE_URL";
        
        logTestInfo("🤖 Executing AI-generated test");
        logTestInfo("📝 Prompt: " + prompt);
        logTestInfo("🌐 URL: " + baseUrl);
        
        try {
            List<TestCase> testCases = testGenerator.generateTestCases(prompt, baseUrl);
            
            if (!testCases.isEmpty()) {
                TestCase testCase = testCases.get(0);
                logTestInfo("✅ Generated test case: " + testCase.getName());
                
                executeGeneratedTestCase(testCase);
                logTestPass("🎉 AI-generated test completed successfully!");
            } else {
                logTestInfo("⚠️ No test cases generated, executing basic navigation");
                navigateTo(baseUrl);
                takeScreenshot("Basic navigation completed");
            }
            
        } catch (Exception e) {
            logTestFail("❌ Test execution failed: " + e.getMessage());
            throw e;
        }
    }
    
    private void executeGeneratedTestCase(TestCase testCase) {
        testCase.getSteps().forEach(step -> {
            logTestInfo("🔄 Step " + step.getStepNumber() + ": " + step.getDescription());
            
            try {
                switch (step.getAction()) {
                    case "NAVIGATE":
                        navigateTo(step.getInputData() != null ? step.getInputData() : testCase.getUrl());
                        break;
                    case "CLICK":
                        if (step.getLocator() != null) {
                            getPage().click(step.getLocator());
                        }
                        break;
                    case "TYPE":
                        if (step.getLocator() != null && step.getInputData() != null) {
                            getPage().fill(step.getLocator(), step.getInputData());
                        }
                        break;
                    case "VERIFY":
                        if (step.getExpectedResult() != null) {
                            getPage().waitForSelector("body", new com.microsoft.playwright.Page.WaitForSelectorOptions().setTimeout(5000));
                        }
                        break;
                    case "WAIT":
                        int waitTime = 2000;
                        if (step.getInputData() != null) {
                            try {
                                waitTime = Integer.parseInt(step.getInputData()) * 1000;
                            } catch (NumberFormatException ignored) {}
                        }
                        getPage().waitForTimeout(waitTime);
                        break;
                }
                
                logTestPass("✅ Step completed: " + step.getDescription());
                
            } catch (Exception e) {
                logTestInfo("⚠️ Step had issues: " + step.getDescription() + " - " + e.getMessage());
            }
        });
        
        takeScreenshot("Generated test completed");
    }
}
EOF

echo "✅ Generated test class: $TEST_FILE"
echo ""

# Compile the new test
echo "🔨 Compiling generated test..."
mvn test-compile -s settings.xml -Dmaven.exec.skip=true -q

if [ $? -eq 0 ]; then
    echo "✅ Compilation successful"
    echo ""
    
    # Run the generated test
    echo "🚀 Running generated test..."
    mvn test -Dtest="$TEST_CLASS_NAME" -s settings.xml -Dmaven.exec.skip=true -Dautomation.headless=true
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "🎉 Test execution completed successfully!"
        echo "📊 Check test results in: test-results/"
        echo "📸 Screenshots saved in: test-results/screenshots/"
    else
        echo ""
        echo "⚠️ Test execution had issues, but that's normal for AI-generated tests"
        echo "💡 Try refining your prompt or check the logs for details"
    fi
else
    echo "❌ Compilation failed"
    exit 1
fi

echo ""
echo "📋 Generated test file: $TEST_FILE"
echo "🗑️ To clean up: rm $TEST_FILE"
EOF
