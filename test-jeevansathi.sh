#!/bin/bash

# 🎯 Jeevansathi.com Test Runner
# Quick script to run your specific test case

echo "🎯 Jeevansathi.com Reset Password Test Runner"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test case details
print_info "Test Case: resetPasswordTest"
print_info "Base URL: https://www.jeevansathi.com/"
print_info "Steps:"
print_info "  1. Click on login button"
print_info "  2. Verify if <PERSON><PERSON> displayed"
print_info "  3. Click on Forgot password"
print_info "  4. Verify if Get OTP button appearing"
echo ""

# Check if user wants to run in headed mode
HEADLESS="true"
if [ "$1" = "--headed" ] || [ "$1" = "-h" ]; then
    HEADLESS="false"
    print_info "Running in HEADED mode (browser will be visible)"
else
    print_info "Running in HEADLESS mode (browser hidden)"
    print_info "Use --headed or -h to see the browser"
fi

echo ""

# Compile first
print_info "Compiling test classes..."
mvn test-compile -s settings.xml -Dmaven.exec.skip=true -q

if [ $? -eq 0 ]; then
    print_success "Compilation successful"
else
    print_error "Compilation failed"
    exit 1
fi

echo ""

# Run the specific test
print_info "Running Jeevansathi Reset Password Test..."
echo ""

mvn test -Dtest=JeevansathiTests#resetPasswordTestManual \
    -s settings.xml \
    -Dmaven.exec.skip=true \
    -Dautomation.headless=$HEADLESS \
    -Dautomation.browser=chromium

TEST_RESULT=$?

echo ""

if [ $TEST_RESULT -eq 0 ]; then
    print_success "🎉 Test PASSED!"
    echo ""
    print_info "📊 Test Results:"
    print_success "  ✅ Login button clicked successfully"
    print_success "  ✅ Register button verification completed"
    print_success "  ✅ Forgot password link clicked"
    print_success "  ✅ Get OTP button verification completed"
    echo ""
    print_info "📸 Screenshots saved in: test-results/screenshots/"
    print_info "📋 Logs available in: test-results/logs/"
else
    print_warning "⚠️ Test had issues (this is normal for website-specific tests)"
    echo ""
    print_info "💡 Possible reasons:"
    print_info "  • Website structure changed"
    print_info "  • Elements have different selectors"
    print_info "  • Page loading took longer than expected"
    print_info "  • Network connectivity issues"
    echo ""
    print_info "🔍 Check the screenshots to see what happened:"
    print_info "  📸 Screenshots: test-results/screenshots/"
    print_info "  📋 Detailed logs: test-results/logs/"
fi

echo ""
print_info "🚀 Other available tests:"
echo "  ./test-jeevansathi.sh --headed    # Run with visible browser"
echo "  mvn test -Dtest=JeevansathiTests#resetPasswordTestAI    # Run AI version"
echo "  mvn test -Dtest=JeevansathiTests#verifyLoginPageElements # Run page verification"
echo "  mvn test -Dtest=JeevansathiTests -s settings.xml -Dmaven.exec.skip=true # Run all Jeevansathi tests"

echo ""
print_info "📋 Test completed at: $(date)"
