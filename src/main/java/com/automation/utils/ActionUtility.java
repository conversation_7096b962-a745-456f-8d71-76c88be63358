package com.automation.utils;

import com.microsoft.playwright.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Action Utility Class - Common actions for all test cases
 */
public class ActionUtility {
    
    private final Page page;
    private final Logger logger;
    
    public ActionUtility(Page page) {
        this.page = page;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }
    
    // Navigation actions
    public void navigateTo(String url) {
        try {
            logger.info("🌐 Navigating to: {}", url);
            page.navigate(url);
            page.waitForLoadState();
            logger.info("✅ Successfully navigated to: {}", url);
        } catch (Exception e) {
            logger.error("❌ Failed to navigate to: {} - {}", url, e.getMessage());
            throw e;
        }
    }
    
    public String getCurrentUrl() {
        return page.url();
    }
    
    public String getPageTitle() {
        String title = page.title();
        logger.info("📄 Page title: {}", title);
        return title;
    }
    
    // Element interaction actions
    public boolean clickElement(String selector) {
        try {
            page.click(selector);
            logger.info("✅ Clicked element: {}", selector);
            return true;
        } catch (Exception e) {
            logger.warn("❌ Failed to click element: {} - {}", selector, e.getMessage());
            return false;
        }
    }
    
    public boolean fillInput(String selector, String text) {
        try {
            page.fill(selector, text);
            logger.info("✅ Filled input {} with: {}", selector, text);
            return true;
        } catch (Exception e) {
            logger.error("❌ Failed to fill input {} - {}", selector, e.getMessage());
            return false;
        }
    }
    
    public boolean selectOption(String selector, String value) {
        try {
            page.selectOption(selector, value);
            logger.info("✅ Selected option {} in: {}", value, selector);
            return true;
        } catch (Exception e) {
            logger.error("❌ Failed to select option {} - {}", value, e.getMessage());
            return false;
        }
    }
    
    // Element verification actions
    public boolean isElementPresent(String selector) {
        try {
            return page.locator(selector).count() > 0;
        } catch (Exception e) {
            logger.debug("Element not present: {}", selector);
            return false;
        }
    }
    
    public boolean isElementVisible(String selector) {
        try {
            return page.locator(selector).isVisible();
        } catch (Exception e) {
            logger.debug("Element not visible: {}", selector);
            return false;
        }
    }
    
    public boolean isElementEnabled(String selector) {
        try {
            return page.locator(selector).isEnabled();
        } catch (Exception e) {
            logger.debug("Element not enabled: {}", selector);
            return false;
        }
    }
    
    public String getElementText(String selector) {
        try {
            String text = page.locator(selector).first().textContent();
            logger.info("📝 Element text: {}", text);
            return text != null ? text : "";
        } catch (Exception e) {
            logger.warn("Could not get text for selector: {}", selector);
            return "";
        }
    }
    
    public String getElementAttribute(String selector, String attribute) {
        try {
            String value = page.locator(selector).getAttribute(attribute);
            return value != null ? value : "";
        } catch (Exception e) {
            logger.warn("Failed to get attribute {} from element: {}", attribute, selector);
            return "";
        }
    }
    
    // Wait actions
    public void waitFor(int milliseconds) {
        try {
            page.waitForTimeout(milliseconds);
            logger.info("⏳ Waited for {}ms", milliseconds);
        } catch (Exception e) {
            logger.warn("Wait interrupted: {}", e.getMessage());
        }
    }
    
    public boolean waitForElementVisible(String selector, int timeoutMs) {
        try {
            page.waitForSelector(selector, new Page.WaitForSelectorOptions()
                .setState(com.microsoft.playwright.options.WaitForSelectorState.VISIBLE)
                .setTimeout(timeoutMs));
            return true;
        } catch (Exception e) {
            logger.warn("Element not visible within {}ms: {}", timeoutMs, selector);
            return false;
        }
    }
    
    public boolean waitForElementHidden(String selector, int timeoutMs) {
        try {
            page.waitForSelector(selector, new Page.WaitForSelectorOptions()
                .setState(com.microsoft.playwright.options.WaitForSelectorState.HIDDEN)
                .setTimeout(timeoutMs));
            return true;
        } catch (Exception e) {
            logger.warn("Element still visible after {}ms: {}", timeoutMs, selector);
            return false;
        }
    }
    
    public void waitForPageLoad() {
        page.waitForLoadState();
        logger.info("⏳ Page load completed");
    }
    
    // Screenshot actions
    public void takeScreenshot(String name) {
        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String fileName = String.format("screenshot_%s_%s.png", 
                name.replaceAll("[^a-zA-Z0-9]", "_"), timestamp);
            page.screenshot(new Page.ScreenshotOptions()
                .setPath(java.nio.file.Paths.get("target/htmlReport/screenshots", fileName)));
            logger.info("📸 Screenshot taken: {}", fileName);
        } catch (Exception e) {
            logger.error("❌ Failed to take screenshot: {}", e.getMessage());
        }
    }
    
    // Utility actions
    public void scrollToElement(String selector) {
        try {
            page.locator(selector).scrollIntoViewIfNeeded();
            logger.info("📜 Scrolled to element: {}", selector);
        } catch (Exception e) {
            logger.warn("Failed to scroll to element: {}", selector);
        }
    }
    
    public void refreshPage() {
        page.reload();
        waitForPageLoad();
        logger.info("🔄 Page refreshed");
    }
    
    public void goBack() {
        page.goBack();
        waitForPageLoad();
        logger.info("⬅️ Navigated back");
    }
    
    public void goForward() {
        page.goForward();
        waitForPageLoad();
        logger.info("➡️ Navigated forward");
    }
    
    public String getPageSource() {
        return page.content();
    }
    
    // Advanced actions
    public boolean clickElementWithText(String text) {
        try {
            String selector = String.format("text=%s", text);
            return clickElement(selector);
        } catch (Exception e) {
            logger.warn("⚠️ Failed to click element with text '{}': {}", text, e.getMessage());
            return false;
        }
    }
    
    public boolean doubleClickElement(String selector) {
        try {
            page.dblclick(selector);
            logger.info("✅ Double-clicked element: {}", selector);
            return true;
        } catch (Exception e) {
            logger.warn("❌ Failed to double-click element: {} - {}", selector, e.getMessage());
            return false;
        }
    }
    
    public boolean rightClickElement(String selector) {
        try {
            page.locator(selector).click(new com.microsoft.playwright.Locator.ClickOptions()
                .setButton(com.microsoft.playwright.options.MouseButton.RIGHT));
            logger.info("✅ Right-clicked element: {}", selector);
            return true;
        } catch (Exception e) {
            logger.warn("❌ Failed to right-click element: {} - {}", selector, e.getMessage());
            return false;
        }
    }
    
    public boolean hoverElement(String selector) {
        try {
            page.hover(selector);
            logger.info("✅ Hovered over element: {}", selector);
            return true;
        } catch (Exception e) {
            logger.warn("❌ Failed to hover over element: {} - {}", selector, e.getMessage());
            return false;
        }
    }
    
    public void pressKey(String key) {
        try {
            page.keyboard().press(key);
            logger.info("⌨️ Pressed key: {}", key);
        } catch (Exception e) {
            logger.warn("❌ Failed to press key: {} - {}", key, e.getMessage());
        }
    }
    
    public void typeText(String text) {
        try {
            page.keyboard().type(text);
            logger.info("⌨️ Typed text: {}", text);
        } catch (Exception e) {
            logger.warn("❌ Failed to type text: {} - {}", text, e.getMessage());
        }
    }
    
    // Multiple element actions
    public int getElementCount(String selector) {
        try {
            return page.locator(selector).count();
        } catch (Exception e) {
            logger.debug("Failed to count elements: {}", selector);
            return 0;
        }
    }
    
    public boolean clickElementByIndex(String selector, int index) {
        try {
            page.locator(selector).nth(index).click();
            logger.info("✅ Clicked element {} at index: {}", selector, index);
            return true;
        } catch (Exception e) {
            logger.warn("❌ Failed to click element {} at index {}: {}", selector, index, e.getMessage());
            return false;
        }
    }
    
    public String getElementTextByIndex(String selector, int index) {
        try {
            String text = page.locator(selector).nth(index).textContent();
            return text != null ? text : "";
        } catch (Exception e) {
            logger.warn("Failed to get text for element {} at index {}", selector, index);
            return "";
        }
    }
}
