package com.automation.utils;

import com.automation.config.ConfigManager;
import com.automation.core.DriverManager;
import com.microsoft.playwright.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Screenshot Utility Class for capturing and managing screenshots
 * Supports different screenshot types and automatic cleanup
 */
public class ScreenshotUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(ScreenshotUtils.class);
    private static final ConfigManager config = ConfigManager.getInstance();
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss-SSS");
    
    /**
     * Take screenshot with default naming
     */
    public static String takeScreenshot() {
        return takeScreenshot("screenshot");
    }
    
    /**
     * Take screenshot with custom name
     */
    public static String takeScreenshot(String name) {
        try {
            Page page = DriverManager.getPage();
            if (page == null) {
                logger.warn("Cannot take screenshot - page is null");
                return null;
            }
            
            String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
            String fileName = sanitizeFileName(name) + "_" + timestamp + ".png";
            String screenshotPath = getScreenshotPath(fileName);
            
            // Ensure directory exists
            createDirectoryIfNotExists(Paths.get(screenshotPath).getParent());
            
            // Take screenshot
            page.screenshot(new Page.ScreenshotOptions()
                    .setPath(Paths.get(screenshotPath))
                    .setFullPage(true));
            
            logger.info("Screenshot captured: {}", screenshotPath);
            return screenshotPath;
            
        } catch (Exception e) {
            logger.error("Failed to take screenshot: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Take screenshot of specific element
     */
    public static String takeElementScreenshot(String locator, String name) {
        try {
            Page page = DriverManager.getPage();
            if (page == null) {
                logger.warn("Cannot take element screenshot - page is null");
                return null;
            }
            
            String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
            String fileName = sanitizeFileName(name) + "_element_" + timestamp + ".png";
            String screenshotPath = getScreenshotPath(fileName);
            
            // Ensure directory exists
            createDirectoryIfNotExists(Paths.get(screenshotPath).getParent());
            
            // Take element screenshot
            page.locator(locator).screenshot(new Page.ScreenshotOptions()
                    .setPath(Paths.get(screenshotPath)));
            
            logger.info("Element screenshot captured: {}", screenshotPath);
            return screenshotPath;
            
        } catch (Exception e) {
            logger.error("Failed to take element screenshot: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Take screenshot with specific dimensions
     */
    public static String takeScreenshotWithDimensions(String name, int width, int height) {
        try {
            Page page = DriverManager.getPage();
            if (page == null) {
                logger.warn("Cannot take screenshot - page is null");
                return null;
            }
            
            String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
            String fileName = sanitizeFileName(name) + "_" + width + "x" + height + "_" + timestamp + ".png";
            String screenshotPath = getScreenshotPath(fileName);
            
            // Ensure directory exists
            createDirectoryIfNotExists(Paths.get(screenshotPath).getParent());
            
            // Set viewport size and take screenshot
            page.setViewportSize(width, height);
            page.screenshot(new Page.ScreenshotOptions()
                    .setPath(Paths.get(screenshotPath))
                    .setFullPage(false));
            
            logger.info("Screenshot with dimensions {}x{} captured: {}", width, height, screenshotPath);
            return screenshotPath;
            
        } catch (Exception e) {
            logger.error("Failed to take screenshot with dimensions: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Take screenshot for test failure
     */
    public static String takeFailureScreenshot(String testName) {
        if (!config.isScreenshotOnFailure()) {
            logger.debug("Screenshot on failure is disabled");
            return null;
        }
        
        String screenshotPath = takeScreenshot("FAILED_" + testName);
        if (screenshotPath != null) {
            logger.info("Failure screenshot captured for test: {}", testName);
        }
        return screenshotPath;
    }
    
    /**
     * Take screenshot for test success
     */
    public static String takeSuccessScreenshot(String testName) {
        if (!config.isScreenshotOnSuccess()) {
            logger.debug("Screenshot on success is disabled");
            return null;
        }
        
        String screenshotPath = takeScreenshot("PASSED_" + testName);
        if (screenshotPath != null) {
            logger.info("Success screenshot captured for test: {}", testName);
        }
        return screenshotPath;
    }
    
    /**
     * Get screenshot directory path
     */
    public static String getScreenshotDirectory() {
        return config.getScreenshotsPath();
    }
    
    /**
     * Get full screenshot path
     */
    private static String getScreenshotPath(String fileName) {
        return Paths.get(getScreenshotDirectory(), fileName).toString();
    }
    
    /**
     * Sanitize file name to remove invalid characters
     */
    private static String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "screenshot";
        }
        
        // Replace invalid characters with underscore
        return fileName.replaceAll("[^a-zA-Z0-9._-]", "_")
                      .replaceAll("_{2,}", "_")  // Replace multiple underscores with single
                      .replaceAll("^_|_$", ""); // Remove leading/trailing underscores
    }
    
    /**
     * Create directory if it doesn't exist
     */
    private static void createDirectoryIfNotExists(Path directory) {
        try {
            if (directory != null && !Files.exists(directory)) {
                Files.createDirectories(directory);
                logger.debug("Created directory: {}", directory);
            }
        } catch (IOException e) {
            logger.error("Failed to create directory: {}", directory, e);
        }
    }
    
    /**
     * Clean up old screenshots based on retention policy
     */
    public static void cleanupOldScreenshots() {
        try {
            int retentionDays = config.getIntProperty("screenshot.retention.days", 7);
            Path screenshotDir = Paths.get(getScreenshotDirectory());
            
            if (!Files.exists(screenshotDir)) {
                return;
            }
            
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(retentionDays);
            
            Files.walk(screenshotDir)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".png"))
                    .filter(path -> {
                        try {
                            return Files.getLastModifiedTime(path)
                                    .toInstant()
                                    .isBefore(cutoffDate.atZone(java.time.ZoneId.systemDefault()).toInstant());
                        } catch (IOException e) {
                            logger.warn("Could not get last modified time for: {}", path);
                            return false;
                        }
                    })
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                            logger.debug("Deleted old screenshot: {}", path);
                        } catch (IOException e) {
                            logger.warn("Could not delete old screenshot: {}", path, e);
                        }
                    });
            
            logger.info("Screenshot cleanup completed. Retention: {} days", retentionDays);
            
        } catch (Exception e) {
            logger.error("Error during screenshot cleanup: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Get screenshot file size in bytes
     */
    public static long getScreenshotSize(String screenshotPath) {
        try {
            Path path = Paths.get(screenshotPath);
            if (Files.exists(path)) {
                return Files.size(path);
            }
        } catch (IOException e) {
            logger.warn("Could not get size of screenshot: {}", screenshotPath, e);
        }
        return 0;
    }
    
    /**
     * Check if screenshot exists
     */
    public static boolean screenshotExists(String screenshotPath) {
        return screenshotPath != null && Files.exists(Paths.get(screenshotPath));
    }
    
    /**
     * Get relative path for reporting
     */
    public static String getRelativeScreenshotPath(String absolutePath) {
        if (absolutePath == null) {
            return null;
        }
        
        try {
            Path absolute = Paths.get(absolutePath);
            Path base = Paths.get(System.getProperty("user.dir"));
            return base.relativize(absolute).toString();
        } catch (Exception e) {
            logger.warn("Could not get relative path for: {}", absolutePath);
            return absolutePath;
        }
    }
}
