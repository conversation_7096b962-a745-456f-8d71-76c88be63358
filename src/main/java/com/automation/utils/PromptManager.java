package com.automation.utils;

import com.automation.generator.UniversalPromptProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.Scanner;

/**
 * Prompt Manager - Utility for managing prompt files
 * Provides easy interface for creating, listing, and managing prompts
 */
public class PromptManager {
    
    private static final Logger logger = LoggerFactory.getLogger(PromptManager.class);
    private final UniversalPromptProcessor promptProcessor;
    
    public PromptManager() {
        this.promptProcessor = new UniversalPromptProcessor();
    }
    
    /**
     * Interactive prompt creation
     */
    public void createPromptInteractively() {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("\n🎯 Create New Test Prompt");
        System.out.println("========================");
        
        System.out.print("📝 Enter module name (e.g., login, search, profile): ");
        String moduleName = scanner.nextLine().trim();
        
        System.out.print("📄 Enter test description/prompt: ");
        String promptContent = scanner.nextLine().trim();
        
        if (moduleName.isEmpty() || promptContent.isEmpty()) {
            System.out.println("❌ Module name and prompt content are required!");
            return;
        }
        
        String fileName = moduleName.toLowerCase().replace(" ", "_") + "_module.txt";
        promptProcessor.createPromptFile(fileName, promptContent);
        
        System.out.println("✅ Prompt file created: " + fileName);
        System.out.println("📁 Location: src/test/resources/prompts/pending/" + fileName);
        System.out.println("🚀 Run tests to process this prompt automatically!");
    }
    
    /**
     * List all pending prompts
     */
    public void listPendingPrompts() {
        System.out.println("\n📋 Pending Prompt Files");
        System.out.println("=======================");
        
        List<String> pendingPrompts = promptProcessor.listPendingPrompts();
        
        if (pendingPrompts.isEmpty()) {
            System.out.println("📭 No pending prompts found.");
            System.out.println("💡 Create new prompts in: src/test/resources/prompts/pending/");
            return;
        }
        
        for (int i = 0; i < pendingPrompts.size(); i++) {
            System.out.println((i + 1) + ". " + pendingPrompts.get(i));
        }
        
        System.out.println("\n🚀 Run universal tests to process these prompts!");
    }
    
    /**
     * Show processing statistics
     */
    public void showStatistics() {
        System.out.println("\n📊 Prompt Processing Statistics");
        System.out.println("===============================");
        
        Map<String, Integer> stats = promptProcessor.getProcessingStats();
        
        System.out.println("📋 Pending prompts: " + stats.get("pending"));
        System.out.println("✅ Processed prompts: " + stats.get("processed"));
        System.out.println("🧪 Generated tests: " + stats.get("generated"));
        
        if (stats.get("pending") > 0) {
            System.out.println("\n🚀 Ready to process " + stats.get("pending") + " prompt(s)!");
        } else {
            System.out.println("\n💡 Add new .txt files to src/test/resources/prompts/pending/ to create tests");
        }
    }
    
    /**
     * Show prompt file content
     */
    public void showPromptContent(String fileName) {
        try {
            Path promptPath = Paths.get("src/test/resources/prompts/pending", fileName);
            if (Files.exists(promptPath)) {
                String content = Files.readString(promptPath);
                System.out.println("\n📄 Content of " + fileName + ":");
                System.out.println("================================");
                System.out.println(content);
            } else {
                System.out.println("❌ Prompt file not found: " + fileName);
            }
        } catch (IOException e) {
            System.out.println("❌ Error reading prompt file: " + e.getMessage());
        }
    }
    
    /**
     * Create sample prompts for different modules
     */
    public void createSamplePrompts() {
        System.out.println("\n🎯 Creating Sample Prompts for Different Modules");
        System.out.println("================================================");
        
        // E-commerce module
        promptProcessor.createPromptFile("ecommerce_module.txt", 
            "Navigate to shopping website, search for products, add to cart, proceed to checkout, enter payment details, complete purchase, verify order confirmation");
        
        // Banking module
        promptProcessor.createPromptFile("banking_module.txt",
            "Navigate to bank website, login with credentials, check account balance, transfer money to another account, verify transaction history, logout securely");
        
        // Social media module
        promptProcessor.createPromptFile("social_module.txt",
            "Navigate to social media platform, create new post, upload image, add caption, share post, verify post appears in timeline, check likes and comments");
        
        // Travel booking module
        promptProcessor.createPromptFile("travel_module.txt",
            "Navigate to travel website, search for flights, select departure and return dates, choose flight, enter passenger details, make payment, verify booking confirmation");
        
        // Healthcare module
        promptProcessor.createPromptFile("healthcare_module.txt",
            "Navigate to healthcare portal, book appointment with doctor, select available time slot, enter patient details, confirm appointment, verify booking email");
        
        System.out.println("✅ Sample prompts created for multiple modules!");
        System.out.println("📁 Check: src/test/resources/prompts/pending/");
        System.out.println("🚀 Run universal tests to generate and execute test cases!");
    }
    
    /**
     * Main method for standalone usage
     */
    public static void main(String[] args) {
        PromptManager manager = new PromptManager();
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            System.out.println("\n🎯 Prompt Manager - Universal Test Generation");
            System.out.println("=============================================");
            System.out.println("1. Create new prompt");
            System.out.println("2. List pending prompts");
            System.out.println("3. Show statistics");
            System.out.println("4. Create sample prompts");
            System.out.println("5. Exit");
            System.out.print("\nChoose option (1-5): ");
            
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    manager.createPromptInteractively();
                    break;
                case "2":
                    manager.listPendingPrompts();
                    break;
                case "3":
                    manager.showStatistics();
                    break;
                case "4":
                    manager.createSamplePrompts();
                    break;
                case "5":
                    System.out.println("👋 Goodbye!");
                    return;
                default:
                    System.out.println("❌ Invalid option. Please choose 1-5.");
            }
        }
    }
}
