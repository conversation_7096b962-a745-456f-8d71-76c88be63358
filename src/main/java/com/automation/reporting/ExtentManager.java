package com.automation.reporting;

import com.automation.config.ConfigManager;
import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.Status;
import com.aventstack.extentreports.reporter.ExtentSparkReporter;
import com.aventstack.extentreports.reporter.configuration.Theme;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * ExtentReports Manager for handling test reporting
 * Manages ExtentReports lifecycle and test logging
 */
public class ExtentManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ExtentManager.class);
    private static final ConfigManager config = ConfigManager.getInstance();
    private static ExtentReports extent;
    private static final ThreadLocal<ExtentTest> test = new ThreadLocal<>();
    private static String reportPath;
    
    /**
     * Initialize ExtentReports
     */
    public static synchronized void initializeExtentReports() {
        if (extent == null) {
            try {
                // Create reports directory
                String reportsDir = config.getReportsPath();
                createDirectoryIfNotExists(Paths.get(reportsDir));
                
                // Generate report file name with timestamp
                String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
                String reportFileName = "ExtentReport_" + timestamp + ".html";
                reportPath = Paths.get(reportsDir, reportFileName).toString();
                
                // Create ExtentSparkReporter
                ExtentSparkReporter sparkReporter = new ExtentSparkReporter(reportPath);
                configureSparkReporter(sparkReporter);
                
                // Create ExtentReports instance
                extent = new ExtentReports();
                extent.attachReporter(sparkReporter);
                
                // Set system information
                setSystemInformation();
                
                logger.info("ExtentReports initialized. Report path: {}", reportPath);
                
            } catch (Exception e) {
                logger.error("Failed to initialize ExtentReports: {}", e.getMessage(), e);
            }
        }
    }
    
    /**
     * Configure Spark Reporter
     */
    private static void configureSparkReporter(ExtentSparkReporter sparkReporter) {
        sparkReporter.config().setTheme(Theme.STANDARD);
        sparkReporter.config().setDocumentTitle("Playwright Automation Test Report");
        sparkReporter.config().setReportName("Test Execution Report");
        sparkReporter.config().setTimeStampFormat("yyyy-MM-dd HH:mm:ss");
        sparkReporter.config().setEncoding("UTF-8");
        
        // Custom CSS for better appearance
        String css = ".test-content { font-size: 14px; }" +
                     ".step-details { margin-left: 20px; }" +
                     ".screenshot-container { text-align: center; margin: 10px 0; }" +
                     ".screenshot-container img { max-width: 100%; height: auto; border: 1px solid #ddd; }";
        sparkReporter.config().setCss(css);
    }
    
    /**
     * Set system information in the report
     */
    private static void setSystemInformation() {
        extent.setSystemInfo("Operating System", System.getProperty("os.name"));
        extent.setSystemInfo("OS Version", System.getProperty("os.version"));
        extent.setSystemInfo("Java Version", System.getProperty("java.version"));
        extent.setSystemInfo("User", System.getProperty("user.name"));
        extent.setSystemInfo("Environment", config.getEnvironment());
        extent.setSystemInfo("Browser", config.getBrowser());
        extent.setSystemInfo("Base URL", config.getBaseUrl());
        extent.setSystemInfo("Headless Mode", String.valueOf(config.isHeadless()));
        extent.setSystemInfo("Parallel Execution", String.valueOf(config.isParallelTests()));
        extent.setSystemInfo("Thread Count", String.valueOf(config.getThreadCount()));
    }
    
    /**
     * Create a new test in the report
     */
    public static ExtentTest createTest(String testName) {
        return createTest(testName, "");
    }
    
    /**
     * Create a new test with description
     */
    public static ExtentTest createTest(String testName, String description) {
        if (extent == null) {
            initializeExtentReports();
        }
        
        ExtentTest extentTest = extent.createTest(testName, description);
        test.set(extentTest);
        logger.debug("Created ExtentTest: {}", testName);
        return extentTest;
    }
    
    /**
     * Get current test instance
     */
    public static ExtentTest getTest() {
        return test.get();
    }
    
    /**
     * Log info message
     */
    public static void logInfo(String message) {
        ExtentTest currentTest = getTest();
        if (currentTest != null) {
            currentTest.log(Status.INFO, message);
        }
    }
    
    /**
     * Log pass message
     */
    public static void logPass(String message) {
        ExtentTest currentTest = getTest();
        if (currentTest != null) {
            currentTest.log(Status.PASS, message);
        }
    }
    
    /**
     * Log fail message
     */
    public static void logFail(String message) {
        ExtentTest currentTest = getTest();
        if (currentTest != null) {
            currentTest.log(Status.FAIL, message);
        }
    }
    
    /**
     * Log fail message with throwable
     */
    public static void logFail(String message, Throwable throwable) {
        ExtentTest currentTest = getTest();
        if (currentTest != null) {
            currentTest.log(Status.FAIL, message);
            currentTest.log(Status.FAIL, throwable);
        }
    }
    
    /**
     * Log warning message
     */
    public static void logWarning(String message) {
        ExtentTest currentTest = getTest();
        if (currentTest != null) {
            currentTest.log(Status.WARNING, message);
        }
    }
    
    /**
     * Log skip message
     */
    public static void logSkip(String message) {
        ExtentTest currentTest = getTest();
        if (currentTest != null) {
            currentTest.log(Status.SKIP, message);
        }
    }
    
    /**
     * Add screenshot to the report
     */
    public static void addScreenshot(String screenshotPath) {
        addScreenshot(screenshotPath, "Screenshot");
    }
    
    /**
     * Add screenshot with custom title
     */
    public static void addScreenshot(String screenshotPath, String title) {
        ExtentTest currentTest = getTest();
        if (currentTest != null && screenshotPath != null) {
            try {
                // Convert to relative path for the report
                String relativePath = getRelativePath(screenshotPath);
                currentTest.addScreenCaptureFromPath(relativePath, title);
                logger.debug("Added screenshot to report: {}", relativePath);
            } catch (Exception e) {
                logger.warn("Failed to add screenshot to report: {}", e.getMessage());
            }
        }
    }
    
    /**
     * Add screenshot with status
     */
    public static void addScreenshot(String screenshotPath, Status status, String message) {
        ExtentTest currentTest = getTest();
        if (currentTest != null && screenshotPath != null) {
            try {
                String relativePath = getRelativePath(screenshotPath);
                currentTest.log(status, message, 
                    currentTest.addScreenCaptureFromPath(relativePath).getModel());
            } catch (Exception e) {
                logger.warn("Failed to add screenshot with status to report: {}", e.getMessage());
            }
        }
    }
    
    /**
     * Assign category to current test
     */
    public static void assignCategory(String... categories) {
        ExtentTest currentTest = getTest();
        if (currentTest != null) {
            currentTest.assignCategory(categories);
        }
    }
    
    /**
     * Assign author to current test
     */
    public static void assignAuthor(String... authors) {
        ExtentTest currentTest = getTest();
        if (currentTest != null) {
            currentTest.assignAuthor(authors);
        }
    }
    
    /**
     * Assign device to current test
     */
    public static void assignDevice(String... devices) {
        ExtentTest currentTest = getTest();
        if (currentTest != null) {
            currentTest.assignDevice(devices);
        }
    }
    
    /**
     * Flush the reports
     */
    public static synchronized void flushReports() {
        if (extent != null) {
            extent.flush();
            logger.info("ExtentReports flushed. Report available at: {}", reportPath);
        }
    }
    
    /**
     * Get report path
     */
    public static String getReportPath() {
        return reportPath;
    }
    
    /**
     * Clean up thread local
     */
    public static void removeTest() {
        test.remove();
    }
    
    /**
     * Create directory if it doesn't exist
     */
    private static void createDirectoryIfNotExists(Path directory) {
        try {
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
                logger.debug("Created directory: {}", directory);
            }
        } catch (IOException e) {
            logger.error("Failed to create directory: {}", directory, e);
        }
    }
    
    /**
     * Get relative path for screenshots
     */
    private static String getRelativePath(String absolutePath) {
        try {
            Path absolute = Paths.get(absolutePath);
            Path reportDir = Paths.get(reportPath).getParent();
            return reportDir.relativize(absolute).toString();
        } catch (Exception e) {
            logger.warn("Could not get relative path for: {}", absolutePath);
            return absolutePath;
        }
    }
}
