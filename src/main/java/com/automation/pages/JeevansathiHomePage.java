package com.automation.pages;

import com.microsoft.playwright.Page;

/**
 * Jeevansathi Homepage Page Object
 * Contains all elements and actions specific to Jeevansathi homepage
 */
public class JeevansathiHomePage extends BasePage {
    
    // Page URL
    private static final String PAGE_URL = "https://www.jeevansathi.com/";
    
    // ========================================
    // PAGE ELEMENTS (LOCATORS)
    // ========================================
    
    // Navigation Elements
    private static final String LOGIN_BUTTON = "text=/login/i";
    private static final String LOGIN_LINK = "a[href*='login']";
    private static final String LOGIN_BTN_ALT = "#login-btn";
    private static final String REGISTER_BUTTON = "text=/register/i";
    private static final String REGISTER_LINK = "a[href*='register']";
    
    // Search Elements
    private static final String SEARCH_BOX = "input[type='search']";
    private static final String SEARCH_INPUT = "input[placeholder*='search']";
    private static final String SEARCH_BUTTON = "button[type='submit']";
    
    // Header Elements
    private static final String LOGO = ".logo";
    private static final String MAIN_MENU = ".main-menu";
    private static final String USER_MENU = ".user-menu";
    
    // Content Elements
    private static final String HERO_SECTION = ".hero-section";
    private static final String FEATURED_PROFILES = ".featured-profiles";
    private static final String SUCCESS_STORIES = ".success-stories";
    
    // Footer Elements
    private static final String FOOTER = "footer";
    private static final String CONTACT_INFO = ".contact-info";
    
    public JeevansathiHomePage(Page page) {
        super(page);
    }
    
    // ========================================
    // PAGE ACTIONS
    // ========================================
    
    /**
     * Navigate to Jeevansathi homepage
     */
    public JeevansathiHomePage navigateToHomepage() {
        navigateTo(PAGE_URL);
        waitForPageLoad();
        logger.info("🏠 Navigated to Jeevansathi homepage");
        return this;
    }
    
    /**
     * Click login button with multiple fallback strategies
     */
    public boolean clickLoginButton() {
        logger.info("🔐 Attempting to click login button");
        
        // Try multiple login button selectors
        String[] loginSelectors = {
            LOGIN_BUTTON,
            LOGIN_LINK,
            LOGIN_BTN_ALT,
            "button:has-text('Login')",
            "a:has-text('Login')",
            "button:has-text('Sign In')",
            "a:has-text('Sign In')",
            "[data-testid='login']",
            ".login-btn",
            ".login-button"
        };
        
        for (String selector : loginSelectors) {
            if (isElementVisible(selector)) {
                if (clickElement(selector)) {
                    logger.info("✅ Successfully clicked login button using: {}", selector);
                    waitFor(2000); // Wait for page transition
                    return true;
                }
            }
        }
        
        logger.warn("⚠️ Could not find or click login button");
        return false;
    }
    
    /**
     * Click register button
     */
    public boolean clickRegisterButton() {
        logger.info("📝 Attempting to click register button");
        
        String[] registerSelectors = {
            REGISTER_BUTTON,
            REGISTER_LINK,
            "button:has-text('Register')",
            "a:has-text('Register')",
            "button:has-text('Sign Up')",
            "a:has-text('Sign Up')",
            "[data-testid='register']",
            ".register-btn",
            ".register-button"
        };
        
        for (String selector : registerSelectors) {
            if (isElementVisible(selector)) {
                if (clickElement(selector)) {
                    logger.info("✅ Successfully clicked register button using: {}", selector);
                    waitFor(2000);
                    return true;
                }
            }
        }
        
        logger.warn("⚠️ Could not find or click register button");
        return false;
    }
    
    /**
     * Perform search
     */
    public boolean performSearch(String searchTerm) {
        logger.info("🔍 Performing search for: {}", searchTerm);
        
        // Try to find search box
        String[] searchSelectors = {SEARCH_BOX, SEARCH_INPUT, "input[name='search']", "#search"};
        
        for (String selector : searchSelectors) {
            if (isElementVisible(selector)) {
                if (fillInput(selector, searchTerm)) {
                    // Try to click search button
                    if (clickElement(SEARCH_BUTTON) || clickElementWithText("Search")) {
                        logger.info("✅ Search performed successfully");
                        return true;
                    }
                    // If no search button, try pressing Enter
                    page.keyboard().press("Enter");
                    logger.info("✅ Search performed using Enter key");
                    return true;
                }
            }
        }
        
        logger.warn("⚠️ Could not perform search");
        return false;
    }
    
    // ========================================
    // PAGE VERIFICATIONS
    // ========================================
    
    /**
     * Check if homepage is loaded
     */
    public boolean isHomepageLoaded() {
        try {
            // Check for key homepage elements
            boolean urlCorrect = getCurrentUrl().contains("jeevansathi.com");
            boolean titlePresent = !getPageTitle().isEmpty();
            boolean bodyPresent = isElementPresent("body");
            
            logger.info("🔍 Homepage load check - URL: {}, Title: {}, Body: {}", 
                       urlCorrect, titlePresent, bodyPresent);
            
            return urlCorrect && titlePresent && bodyPresent;
        } catch (Exception e) {
            logger.error("❌ Error checking homepage load: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Check if login button is displayed
     */
    public boolean isLoginButtonDisplayed() {
        String[] loginSelectors = {
            LOGIN_BUTTON, LOGIN_LINK, LOGIN_BTN_ALT,
            "button:has-text('Login')", "a:has-text('Login')"
        };
        
        for (String selector : loginSelectors) {
            if (isElementVisible(selector)) {
                logger.info("✅ Login button found: {}", selector);
                return true;
            }
        }
        
        logger.info("⚠️ Login button not visible");
        return false;
    }
    
    /**
     * Check if register button is displayed
     */
    public boolean isRegisterButtonDisplayed() {
        String[] registerSelectors = {
            REGISTER_BUTTON, REGISTER_LINK,
            "button:has-text('Register')", "a:has-text('Register')",
            "button:has-text('Sign Up')", "a:has-text('Sign Up')"
        };
        
        for (String selector : registerSelectors) {
            if (isElementVisible(selector)) {
                logger.info("✅ Register button found: {}", selector);
                return true;
            }
        }
        
        logger.info("⚠️ Register button not visible");
        return false;
    }
    
    /**
     * Check if search functionality is available
     */
    public boolean isSearchAvailable() {
        String[] searchSelectors = {SEARCH_BOX, SEARCH_INPUT, "input[name='search']"};
        
        for (String selector : searchSelectors) {
            if (isElementVisible(selector)) {
                logger.info("✅ Search functionality found: {}", selector);
                return true;
            }
        }
        
        logger.info("⚠️ Search functionality not available");
        return false;
    }
    
    /**
     * Get all visible navigation elements
     */
    public int getVisibleNavigationElementsCount() {
        int count = 0;
        
        if (isLoginButtonDisplayed()) count++;
        if (isRegisterButtonDisplayed()) count++;
        if (isSearchAvailable()) count++;
        if (isElementVisible(LOGO)) count++;
        if (isElementVisible(MAIN_MENU)) count++;
        
        logger.info("📊 Found {} visible navigation elements", count);
        return count;
    }
    
    // ========================================
    // PAGE UTILITIES
    // ========================================
    
    /**
     * Wait for homepage to fully load
     */
    public JeevansathiHomePage waitForHomepageLoad() {
        waitForPageLoad();
        
        // Wait for key elements to be visible
        waitForElementVisible("body", 10000);
        
        // Additional wait for dynamic content
        waitFor(2000);
        
        logger.info("⏳ Homepage fully loaded");
        return this;
    }
    
    /**
     * Take homepage screenshot
     */
    public JeevansathiHomePage captureHomepageScreenshot(String description) {
        takeScreenshot("homepage_" + description);
        return this;
    }
    
    /**
     * Get homepage title
     */
    public String getHomepageTitle() {
        String title = getPageTitle();
        logger.info("📄 Homepage title: {}", title);
        return title;
    }
    
    /**
     * Get homepage URL
     */
    public String getHomepageUrl() {
        String url = getCurrentUrl();
        logger.info("🌐 Homepage URL: {}", url);
        return url;
    }
    
    /**
     * Check if page contains specific text
     */
    public boolean pageContainsText(String text) {
        try {
            return page.locator("text=" + text).isVisible();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Get page load time (approximate)
     */
    public long getPageLoadTime() {
        // This is a simplified implementation
        // In real scenarios, you might use Performance API
        long startTime = System.currentTimeMillis();
        waitForPageLoad();
        long endTime = System.currentTimeMillis();
        long loadTime = endTime - startTime;
        
        logger.info("⏱️ Page load time: {}ms", loadTime);
        return loadTime;
    }
}
