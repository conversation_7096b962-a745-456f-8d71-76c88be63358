package com.automation.pages;

import com.microsoft.playwright.Page;

/**
 * Jeevansathi Login Page Object
 */
public class JeevansathiLoginPage extends BasePage {

    // Login Form Elements
    private static final String USERNAME_INPUT = "input[name='username']";
    private static final String EMAIL_INPUT = "input[type='email']";
    private static final String PASSWORD_INPUT = "input[type='password']";
    private static final String LOGIN_SUBMIT_BUTTON = "button[type='submit']";
    private static final String LOGIN_BUTTON = "button:has-text('Login')";
    
    // Alternative Login Elements
    private static final String USERNAME_ALT = "#username";
    private static final String EMAIL_ALT = "#email";
    private static final String PASSWORD_ALT = "#password";
    private static final String SUBMIT_ALT = "input[type='submit']";
    
    // Registration and Forgot Password Links
    private static final String REGISTER_LINK = "a:has-text('Register')";
    private static final String REGISTER_BUTTON = "button:has-text('Register')";
    private static final String FORGOT_PASSWORD_LINK = "a:has-text('Forgot Password')";
    private static final String FORGOT_PASSWORD_ALT = "text=/forgot.*password/i";
    
    // OTP and Reset Elements
    private static final String GET_OTP_BUTTON = "button:has-text('Get OTP')";
    private static final String SEND_OTP_BUTTON = "button:has-text('Send OTP')";
    private static final String OTP_INPUT = "input[name='otp']";
    private static final String VERIFY_OTP_BUTTON = "button:has-text('Verify OTP')";
    
    // Error and Success Messages
    private static final String ERROR_MESSAGE = ".error-message";
    private static final String SUCCESS_MESSAGE = ".success-message";
    private static final String ALERT_MESSAGE = ".alert";
    
    // Social Login Elements
    private static final String FACEBOOK_LOGIN = "button:has-text('Facebook')";
    private static final String GOOGLE_LOGIN = "button:has-text('Google')";
    
    public JeevansathiLoginPage(Page page) {
        super(page);
    }

    // Enter username or email
    public boolean enterUsername(String username) {
        logger.info("👤 Entering username: {}", username);
        
        String[] usernameSelectors = {
            USERNAME_INPUT, EMAIL_INPUT, USERNAME_ALT, EMAIL_ALT,
            "input[placeholder*='username']", "input[placeholder*='email']"
        };
        
        for (String selector : usernameSelectors) {
            if (isElementVisible(selector)) {
                if (fillInput(selector, username)) {
                    logger.info("✅ Username entered successfully");
                    return true;
                }
            }
        }
        
        logger.warn("⚠️ Could not enter username");
        return false;
    }

    public boolean enterPassword(String password) {
        logger.info("🔒 Entering password");
        
        String[] passwordSelectors = {
            PASSWORD_INPUT, PASSWORD_ALT,
            "input[placeholder*='password']"
        };
        
        for (String selector : passwordSelectors) {
            if (isElementVisible(selector)) {
                if (fillInput(selector, password)) {
                    logger.info("✅ Password entered successfully");
                    return true;
                }
            }
        }
        
        logger.warn("⚠️ Could not enter password");
        return false;
    }

    public boolean clickLoginSubmit() {
        logger.info("🚀 Clicking login submit button");
        
        String[] submitSelectors = {
            LOGIN_SUBMIT_BUTTON, LOGIN_BUTTON, SUBMIT_ALT,
            "button:has-text('Sign In')", "input[value='Login']"
        };
        
        for (String selector : submitSelectors) {
            if (isElementVisible(selector)) {
                if (clickElement(selector)) {
                    logger.info("✅ Login submit clicked successfully");
                    waitFor(3000); // Wait for login processing
                    return true;
                }
            }
        }
        
        logger.warn("⚠️ Could not click login submit");
        return false;
    }

    // Perform complete login
    public boolean performLogin(String username, String password) {
        logger.info("🔐 Performing complete login for user: {}", username);
        
        boolean usernameEntered = enterUsername(username);
        boolean passwordEntered = enterPassword(password);
        boolean submitClicked = clickLoginSubmit();
        
        boolean loginSuccess = usernameEntered && passwordEntered && submitClicked;
        
        if (loginSuccess) {
            logger.info("✅ Login process completed successfully");
        } else {
            logger.warn("⚠️ Login process had issues");
        }
        
        return loginSuccess;
    }

    public boolean clickRegisterButton() {
        logger.info("📝 Clicking register button");
        
        String[] registerSelectors = {
            REGISTER_BUTTON, REGISTER_LINK,
            "a:has-text('Sign Up')", "button:has-text('Sign Up')",
            "a[href*='register']", "button[onclick*='register']"
        };
        
        for (String selector : registerSelectors) {
            if (isElementVisible(selector)) {
                if (clickElement(selector)) {
                    logger.info("✅ Register button clicked successfully");
                    waitFor(2000);
                    return true;
                }
            }
        }
        
        logger.warn("⚠️ Could not click register button");
        return false;
    }

    public boolean clickForgotPasswordLink() {
        logger.info("🔑 Clicking forgot password link");
        
        String[] forgotPasswordSelectors = {
            FORGOT_PASSWORD_LINK, FORGOT_PASSWORD_ALT,
            "a:has-text('Forgot')", "a:has-text('Reset Password')",
            "a[href*='forgot']", "a[href*='reset']"
        };
        
        for (String selector : forgotPasswordSelectors) {
            if (isElementVisible(selector)) {
                if (clickElement(selector)) {
                    logger.info("✅ Forgot password link clicked successfully");
                    waitFor(2000);
                    return true;
                }
            }
        }
        
        logger.warn("⚠️ Could not click forgot password link");
        return false;
    }

    public boolean enterResetEmail(String email) {
        logger.info("📧 Entering email for password reset: {}", email);
        
        String[] emailSelectors = {
            EMAIL_INPUT, "input[name='email']", "input[placeholder*='email']"
        };
        
        for (String selector : emailSelectors) {
            if (isElementVisible(selector)) {
                if (fillInput(selector, email)) {
                    logger.info("✅ Reset email entered successfully");
                    return true;
                }
            }
        }
        
        logger.warn("⚠️ Could not enter reset email");
        return false;
    }

    public boolean clickGetOTPButton() {
        logger.info("📱 Clicking Get OTP button");
        
        String[] otpSelectors = {
            GET_OTP_BUTTON, SEND_OTP_BUTTON,
            "button:has-text('OTP')", "button:has-text('Send')",
            "input[value*='OTP']"
        };
        
        for (String selector : otpSelectors) {
            if (isElementVisible(selector)) {
                if (clickElement(selector)) {
                    logger.info("✅ Get OTP button clicked successfully");
                    waitFor(2000);
                    return true;
                }
            }
        }
        
        logger.warn("⚠️ Could not click Get OTP button");
        return false;
    }

    // Check if login page is loaded
    public boolean isLoginPageLoaded() {
        try {
            boolean hasLoginForm = isElementPresent(USERNAME_INPUT) || isElementPresent(EMAIL_INPUT);
            boolean hasPasswordField = isElementPresent(PASSWORD_INPUT);
            boolean hasSubmitButton = isElementPresent(LOGIN_SUBMIT_BUTTON) || isElementPresent(LOGIN_BUTTON);
            
            logger.info("🔍 Login page check - Form: {}, Password: {}, Submit: {}", 
                       hasLoginForm, hasPasswordField, hasSubmitButton);
            
            return hasLoginForm || hasPasswordField || hasSubmitButton;
        } catch (Exception e) {
            logger.error("❌ Error checking login page: {}", e.getMessage());
            return false;
        }
    }

    public boolean isRegisterButtonDisplayed() {
        String[] registerSelectors = {
            REGISTER_BUTTON, REGISTER_LINK,
            "a:has-text('Register')", "button:has-text('Register')",
            "a:has-text('Sign Up')", "button:has-text('Sign Up')"
        };
        
        for (String selector : registerSelectors) {
            if (isElementVisible(selector)) {
                logger.info("✅ Register button found: {}", selector);
                return true;
            }
        }
        
        logger.info("⚠️ Register button not visible");
        return false;
    }

    public boolean isForgotPasswordLinkDisplayed() {
        String[] forgotSelectors = {
            FORGOT_PASSWORD_LINK, FORGOT_PASSWORD_ALT,
            "a:has-text('Forgot')", "a:has-text('Reset')"
        };
        
        for (String selector : forgotSelectors) {
            if (isElementVisible(selector)) {
                logger.info("✅ Forgot password link found: {}", selector);
                return true;
            }
        }
        
        logger.info("⚠️ Forgot password link not visible");
        return false;
    }

    // Check if Get OTP button is appearing
    public boolean isGetOTPButtonAppearing() {
        String[] otpSelectors = {
            GET_OTP_BUTTON, SEND_OTP_BUTTON,
            "button:has-text('OTP')", "button:has-text('Send')",
            "text=/.*otp.*/i"
        };
        
        for (String selector : otpSelectors) {
            if (isElementVisible(selector)) {
                logger.info("✅ Get OTP button found: {}", selector);
                return true;
            }
        }
        
        logger.info("⚠️ Get OTP button not visible");
        return false;
    }

    public boolean hasErrorMessage() {
        String[] errorSelectors = {
            ERROR_MESSAGE, ALERT_MESSAGE,
            ".error", ".alert-danger", ".message-error"
        };
        
        for (String selector : errorSelectors) {
            if (isElementVisible(selector)) {
                String errorText = getElementText(selector);
                logger.info("⚠️ Error message found: {}", errorText);
                return true;
            }
        }
        
        return false;
    }

    public boolean hasSuccessMessage() {
        String[] successSelectors = {
            SUCCESS_MESSAGE, ".success", ".alert-success", ".message-success"
        };
        
        for (String selector : successSelectors) {
            if (isElementVisible(selector)) {
                String successText = getElementText(selector);
                logger.info("✅ Success message found: {}", successText);
                return true;
            }
        }
        
        return false;
    }

    public JeevansathiLoginPage captureLoginPageScreenshot(String description) {
        takeScreenshot("login_page_" + description);
        return this;
    }

    public JeevansathiLoginPage waitForLoginPageLoad() {
        waitForPageLoad();
        waitFor(2000);
        logger.info("⏳ Login page fully loaded");
        return this;
    }

    public JeevansathiLoginPage clearAllFields() {
        logger.info("🧹 Clearing all form fields");

        String[] inputSelectors = {USERNAME_INPUT, EMAIL_INPUT, PASSWORD_INPUT};

        for (String selector : inputSelectors) {
            if (isElementVisible(selector)) {
                fillInput(selector, "");
            }
        }

        return this;
    }
}
