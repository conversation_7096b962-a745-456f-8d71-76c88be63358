package com.automation.pages;

import com.microsoft.playwright.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * HelpAndSupport Page Object - Page interactions
 */
public class HelpAndSupportPage extends BasePage {

    private static final Logger logger = LoggerFactory.getLogger(HelpAndSupportPage.class);

    // Page elements
    private static final String HELP_BUTTON = "button:has-text('Help')";
    private static final String HELP_LINK = "a:has-text('Help')";
    private static final String SUPPORT_LINK = "a:has-text('Support')";
    private static final String CATEGORIES_SECTION = ".help-categories";
    private static final String CATEGORY_ITEMS = ".category-item";

    public HelpAndSupportPage(Page page) {
        super(page);
    }

    public boolean clickHelpButton() {
        logger.info("🖱️ Clicking Help button");

        String[] selectors = {
            HELP_BUTTON,
            HELP_LINK,
            SUPPORT_LINK,
            "text=/help/i",
            "text=/support/i"
        };

        for (String selector : selectors) {
            if (actionUtility.clickElement(selector)) {
                logger.info("✅ Successfully clicked help element");
                actionUtility.waitFor(2000);
                return true;
            }
        }

        logger.warn("⚠️ Failed to click help button");
        return false;
    }

    public boolean verifyCategoriesDisplayed() {
        logger.info("🔍 Verifying categories are displayed");

        // Check for categories section
        if (actionUtility.isElementPresent(CATEGORIES_SECTION)) {
            logger.info("✅ Categories section found");
            return true;
        }

        // Check for individual category items
        int categoryCount = actionUtility.getElementCount(CATEGORY_ITEMS);
        if (categoryCount > 0) {
            logger.info("✅ Found {} category items", categoryCount);
            return true;
        }

        // Generic check for any help-related content
        String[] fallbackSelectors = {
            ".help-content",
            ".support-content",
            "[class*='help']",
            "[class*='support']",
            "text=/faq/i",
            "text=/contact/i"
        };

        for (String selector : fallbackSelectors) {
            if (actionUtility.isElementPresent(selector)) {
                logger.info("✅ Found help content with selector: {}", selector);
                return true;
            }
        }

        logger.warn("⚠️ No categories or help content found");
        return false;
    }

    public boolean isHelpPageLoaded() {
        return actionUtility.getPageTitle().toLowerCase().contains("help") ||
               actionUtility.isElementPresent(HELP_BUTTON) ||
               actionUtility.isElementPresent(CATEGORIES_SECTION);
    }
}
