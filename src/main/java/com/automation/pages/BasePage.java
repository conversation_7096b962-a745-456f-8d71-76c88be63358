package com.automation.pages;

import com.microsoft.playwright.Page;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.options.WaitForSelectorState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Base Page Class
 * Contains common page operations and utilities for all page objects
 */
public abstract class BasePage {
    
    protected final Page page;
    protected final Logger logger;
    
    public BasePage(Page page) {
        this.page = page;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }
    
    // ========================================
    // NAVIGATION METHODS
    // ========================================
    
    /**
     * Navigate to a specific URL
     */
    protected void navigateTo(String url) {
        try {
            logger.info("🌐 Navigating to: {}", url);
            page.navigate(url);
            page.waitForLoadState();
            logger.info("✅ Successfully navigated to: {}", url);
        } catch (Exception e) {
            logger.error("❌ Failed to navigate to: {} - {}", url, e.getMessage());
            throw e;
        }
    }
    
    /**
     * Get current page URL
     */
    protected String getCurrentUrl() {
        return page.url();
    }
    
    /**
     * Get page title
     */
    protected String getPageTitle() {
        return page.title();
    }
    
    // ========================================
    // ELEMENT INTERACTION METHODS
    // ========================================
    
    /**
     * Click element with multiple fallback strategies
     */
    protected boolean clickElement(String selector) {
        try {
            logger.info("🖱️ Clicking element: {}", selector);
            
            // Wait for element to be visible
            page.waitForSelector(selector, new Page.WaitForSelectorOptions()
                .setState(WaitForSelectorState.VISIBLE)
                .setTimeout(10000));
            
            page.click(selector);
            logger.info("✅ Successfully clicked: {}", selector);
            return true;
            
        } catch (Exception e) {
            logger.warn("⚠️ Failed to click {}: {}", selector, e.getMessage());
            return false;
        }
    }
    
    /**
     * Click element with text content
     */
    protected boolean clickElementWithText(String text) {
        try {
            String selector = String.format("text=%s", text);
            return clickElement(selector);
        } catch (Exception e) {
            logger.warn("⚠️ Failed to click element with text '{}': {}", text, e.getMessage());
            return false;
        }
    }
    
    /**
     * Fill input field
     */
    protected boolean fillInput(String selector, String text) {
        try {
            logger.info("⌨️ Filling input {}: {}", selector, text);
            
            page.waitForSelector(selector, new Page.WaitForSelectorOptions()
                .setState(WaitForSelectorState.VISIBLE)
                .setTimeout(10000));
            
            page.fill(selector, text);
            logger.info("✅ Successfully filled input: {}", selector);
            return true;
            
        } catch (Exception e) {
            logger.error("❌ Failed to fill input {}: {}", selector, e.getMessage());
            return false;
        }
    }
    
    /**
     * Select option from dropdown
     */
    protected boolean selectOption(String selector, String value) {
        try {
            logger.info("📋 Selecting option {} in: {}", value, selector);
            page.selectOption(selector, value);
            logger.info("✅ Successfully selected option: {}", value);
            return true;
        } catch (Exception e) {
            logger.error("❌ Failed to select option {}: {}", value, e.getMessage());
            return false;
        }
    }
    
    // ========================================
    // ELEMENT VERIFICATION METHODS
    // ========================================
    
    /**
     * Check if element is visible
     */
    protected boolean isElementVisible(String selector) {
        try {
            return page.locator(selector).isVisible();
        } catch (Exception e) {
            logger.debug("Element not visible: {}", selector);
            return false;
        }
    }
    
    /**
     * Check if element exists (may not be visible)
     */
    protected boolean isElementPresent(String selector) {
        try {
            return page.locator(selector).count() > 0;
        } catch (Exception e) {
            logger.debug("Element not present: {}", selector);
            return false;
        }
    }
    
    /**
     * Check if element is enabled
     */
    protected boolean isElementEnabled(String selector) {
        try {
            return page.locator(selector).isEnabled();
        } catch (Exception e) {
            logger.debug("Element not enabled: {}", selector);
            return false;
        }
    }
    
    /**
     * Get element text
     */
    protected String getElementText(String selector) {
        try {
            return page.locator(selector).textContent();
        } catch (Exception e) {
            logger.warn("Failed to get text from element: {}", selector);
            return "";
        }
    }
    
    /**
     * Get element attribute
     */
    protected String getElementAttribute(String selector, String attribute) {
        try {
            return page.locator(selector).getAttribute(attribute);
        } catch (Exception e) {
            logger.warn("Failed to get attribute {} from element: {}", attribute, selector);
            return "";
        }
    }
    
    // ========================================
    // WAIT METHODS
    // ========================================
    
    /**
     * Wait for element to be visible
     */
    protected boolean waitForElementVisible(String selector, int timeoutMs) {
        try {
            page.waitForSelector(selector, new Page.WaitForSelectorOptions()
                .setState(WaitForSelectorState.VISIBLE)
                .setTimeout(timeoutMs));
            return true;
        } catch (Exception e) {
            logger.warn("Element not visible within {}ms: {}", timeoutMs, selector);
            return false;
        }
    }
    
    /**
     * Wait for element to disappear
     */
    protected boolean waitForElementHidden(String selector, int timeoutMs) {
        try {
            page.waitForSelector(selector, new Page.WaitForSelectorOptions()
                .setState(WaitForSelectorState.HIDDEN)
                .setTimeout(timeoutMs));
            return true;
        } catch (Exception e) {
            logger.warn("Element still visible after {}ms: {}", timeoutMs, selector);
            return false;
        }
    }
    
    /**
     * Wait for page to load
     */
    protected void waitForPageLoad() {
        page.waitForLoadState();
    }
    
    /**
     * Wait for specific time
     */
    protected void waitFor(int milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Wait interrupted: {}", e.getMessage());
        }
    }
    
    // ========================================
    // UTILITY METHODS
    // ========================================
    
    /**
     * Take screenshot
     */
    protected void takeScreenshot(String name) {
        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String fileName = String.format("screenshot_%s_%s.png", name.replaceAll("[^a-zA-Z0-9]", "_"), timestamp);
            page.screenshot(new Page.ScreenshotOptions().setPath(java.nio.file.Paths.get("target/htmlReport/screenshots", fileName)));
            logger.info("📸 Screenshot taken: {}", fileName);
        } catch (Exception e) {
            logger.error("❌ Failed to take screenshot: {}", e.getMessage());
        }
    }
    
    /**
     * Scroll to element
     */
    protected void scrollToElement(String selector) {
        try {
            page.locator(selector).scrollIntoViewIfNeeded();
            logger.info("📜 Scrolled to element: {}", selector);
        } catch (Exception e) {
            logger.warn("Failed to scroll to element: {}", selector);
        }
    }
    
    /**
     * Get page source
     */
    protected String getPageSource() {
        return page.content();
    }
    
    /**
     * Refresh page
     */
    protected void refreshPage() {
        page.reload();
        waitForPageLoad();
        logger.info("🔄 Page refreshed");
    }
    
    /**
     * Go back in browser history
     */
    protected void goBack() {
        page.goBack();
        waitForPageLoad();
        logger.info("⬅️ Navigated back");
    }
    
    /**
     * Go forward in browser history
     */
    protected void goForward() {
        page.goForward();
        waitForPageLoad();
        logger.info("➡️ Navigated forward");
    }
}
