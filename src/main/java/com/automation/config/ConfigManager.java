package com.automation.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * Configuration Manager for handling application properties
 * Supports environment-specific configurations
 */
public class ConfigManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigManager.class);
    private static ConfigManager instance;
    private Properties properties;
    private String environment;
    
    private ConfigManager() {
        loadConfiguration();
    }
    
    /**
     * Get singleton instance of ConfigManager
     */
    public static synchronized ConfigManager getInstance() {
        if (instance == null) {
            instance = new ConfigManager();
        }
        return instance;
    }
    
    /**
     * Load configuration based on environment
     */
    private void loadConfiguration() {
        properties = new Properties();
        environment = System.getProperty("environment", "dev");
        
        // Load default configuration
        loadPropertiesFile("config.properties");
        
        // Load environment-specific configuration
        String envConfigFile = "config-" + environment + ".properties";
        loadPropertiesFile(envConfigFile);
        
        // Override with system properties
        properties.putAll(System.getProperties());
        
        logger.info("Configuration loaded for environment: {}", environment);
    }
    
    /**
     * Load properties from file
     */
    private void loadPropertiesFile(String fileName) {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(fileName)) {
            if (inputStream != null) {
                properties.load(inputStream);
                logger.debug("Loaded configuration from: {}", fileName);
            } else {
                logger.warn("Configuration file not found: {}", fileName);
            }
        } catch (IOException e) {
            logger.error("Error loading configuration file: {}", fileName, e);
        }
    }
    
    /**
     * Get property value as String
     */
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    /**
     * Get property value with default
     */
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * Get property value as Integer
     */
    public int getIntProperty(String key, int defaultValue) {
        String value = getProperty(key);
        if (value != null) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                logger.warn("Invalid integer value for property {}: {}", key, value);
            }
        }
        return defaultValue;
    }
    
    /**
     * Get property value as Boolean
     */
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = getProperty(key);
        if (value != null) {
            return Boolean.parseBoolean(value);
        }
        return defaultValue;
    }
    
    /**
     * Get property value as Long
     */
    public long getLongProperty(String key, long defaultValue) {
        String value = getProperty(key);
        if (value != null) {
            try {
                return Long.parseLong(value);
            } catch (NumberFormatException e) {
                logger.warn("Invalid long value for property {}: {}", key, value);
            }
        }
        return defaultValue;
    }
    
    // Common configuration getters
    
    public String getBrowser() {
        return getProperty("browser", "chromium");
    }
    
    public boolean isHeadless() {
        return getBooleanProperty("headless", false);
    }
    
    public String getBaseUrl() {
        return getProperty("base.url", "https://example.com");
    }
    
    public String getApiBaseUrl() {
        return getProperty("api.base.url", "https://api.example.com");
    }
    
    public int getDefaultTimeout() {
        return getIntProperty("default.timeout", 30000);
    }
    
    public int getPageLoadTimeout() {
        return getIntProperty("page.load.timeout", 60000);
    }
    
    public int getElementTimeout() {
        return getIntProperty("element.timeout", 10000);
    }
    
    public String getReportsPath() {
        return getProperty("reports.path", "test-results/reports");
    }
    
    public String getScreenshotsPath() {
        return getProperty("screenshots.path", "test-results/screenshots");
    }
    
    public boolean isScreenshotOnFailure() {
        return getBooleanProperty("screenshot.on.failure", true);
    }
    
    public boolean isScreenshotOnSuccess() {
        return getBooleanProperty("screenshot.on.success", false);
    }
    
    public String getOpenAiApiKey() {
        return getProperty("openai.api.key");
    }
    
    public String getAugmentApiKey() {
        return getProperty("augment.api.key");
    }
    
    public boolean isTestGenerationEnabled() {
        return getBooleanProperty("test.generation.enabled", true);
    }
    
    public String getTestGenerationModel() {
        return getProperty("test.generation.model", "gpt-4");
    }
    
    public double getTestGenerationTemperature() {
        String value = getProperty("test.generation.temperature", "0.3");
        try {
            return Double.parseDouble(value);
        } catch (NumberFormatException e) {
            logger.warn("Invalid temperature value: {}", value);
            return 0.3;
        }
    }
    
    public int getTestGenerationMaxTokens() {
        return getIntProperty("test.generation.max.tokens", 2000);
    }
    
    public int getThreadCount() {
        return getIntProperty("thread.count", 3);
    }
    
    public boolean isParallelTests() {
        return getBooleanProperty("parallel.tests", true);
    }
    
    public String getEnvironment() {
        return environment;
    }
    
    /**
     * Reload configuration
     */
    public void reload() {
        loadConfiguration();
        logger.info("Configuration reloaded");
    }
    
    /**
     * Set property programmatically
     */
    public void setProperty(String key, String value) {
        properties.setProperty(key, value);
    }
    
    /**
     * Check if property exists
     */
    public boolean hasProperty(String key) {
        return properties.containsKey(key);
    }
    
    /**
     * Get all properties
     */
    public Properties getAllProperties() {
        return new Properties(properties);
    }
}
