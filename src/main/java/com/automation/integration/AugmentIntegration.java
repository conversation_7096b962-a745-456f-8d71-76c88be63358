package com.automation.integration;

import com.automation.config.ConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Augment Integration Service
 * Handles communication with remote Augment services for AI-powered test generation
 */
public class AugmentIntegration {
    
    private static final Logger logger = LoggerFactory.getLogger(AugmentIntegration.class);
    private final ConfigManager config;
    private final HttpClient httpClient;
    private final String augmentApiUrl;
    private final String apiKey;
    
    public AugmentIntegration() {
        this.config = ConfigManager.getInstance();
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(30))
                .build();
        this.augmentApiUrl = config.getProperty("augment.api.url", "https://api.augmentcode.com");
        this.apiKey = config.getProperty("augment.api.key", "");
    }
    
    /**
     * Generate test steps from natural language description
     */
    public CompletableFuture<AugmentTestResponse> generateTestSteps(String prompt, String baseUrl) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("🤖 Generating test steps from prompt: {}", prompt);
                
                Map<String, Object> requestBody = new HashMap<>();
                requestBody.put("prompt", prompt);
                requestBody.put("baseUrl", baseUrl);
                requestBody.put("framework", "playwright");
                requestBody.put("language", "java");
                requestBody.put("testType", "ui");
                
                String jsonBody = convertToJson(requestBody);
                
                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(augmentApiUrl + "/v1/generate-test"))
                        .header("Content-Type", "application/json")
                        .header("Authorization", "Bearer " + apiKey)
                        .header("User-Agent", "Playwright-Framework/1.0")
                        .timeout(Duration.ofMinutes(2))
                        .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
                        .build();
                
                HttpResponse<String> response = httpClient.send(request, 
                        HttpResponse.BodyHandlers.ofString());
                
                if (response.statusCode() == 200) {
                    logger.info("✅ Successfully generated test steps from Augment");
                    return parseAugmentResponse(response.body());
                } else {
                    logger.warn("⚠️ Augment API returned status: {} - {}", 
                            response.statusCode(), response.body());
                    return createFallbackResponse(prompt, baseUrl);
                }
                
            } catch (Exception e) {
                logger.error("❌ Failed to generate test steps from Augment: {}", e.getMessage());
                return createFallbackResponse(prompt, baseUrl);
            }
        });
    }
    
    /**
     * Get intelligent element locators from Augment
     */
    public CompletableFuture<AugmentLocatorResponse> getSmartLocators(String elementDescription, String pageContext) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("🔍 Getting smart locators for: {}", elementDescription);
                
                Map<String, Object> requestBody = new HashMap<>();
                requestBody.put("elementDescription", elementDescription);
                requestBody.put("pageContext", pageContext);
                requestBody.put("framework", "playwright");
                
                String jsonBody = convertToJson(requestBody);
                
                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(augmentApiUrl + "/v1/smart-locators"))
                        .header("Content-Type", "application/json")
                        .header("Authorization", "Bearer " + apiKey)
                        .timeout(Duration.ofSeconds(30))
                        .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
                        .build();
                
                HttpResponse<String> response = httpClient.send(request, 
                        HttpResponse.BodyHandlers.ofString());
                
                if (response.statusCode() == 200) {
                    logger.info("✅ Successfully received smart locators from Augment");
                    return parseLocatorResponse(response.body());
                } else {
                    logger.warn("⚠️ Smart locator request failed: {}", response.statusCode());
                    return createFallbackLocatorResponse(elementDescription);
                }
                
            } catch (Exception e) {
                logger.error("❌ Failed to get smart locators: {}", e.getMessage());
                return createFallbackLocatorResponse(elementDescription);
            }
        });
    }
    
    /**
     * Send test execution results to Augment for analytics
     */
    public CompletableFuture<Void> sendTestResults(AugmentTestResult testResult) {
        return CompletableFuture.runAsync(() -> {
            try {
                logger.info("📊 Sending test results to Augment analytics");
                
                Map<String, Object> requestBody = new HashMap<>();
                requestBody.put("testName", testResult.getTestName());
                requestBody.put("status", testResult.getStatus());
                requestBody.put("duration", testResult.getDuration());
                requestBody.put("steps", testResult.getSteps());
                requestBody.put("screenshots", testResult.getScreenshots());
                requestBody.put("errors", testResult.getErrors());
                requestBody.put("framework", "playwright");
                requestBody.put("timestamp", System.currentTimeMillis());
                
                String jsonBody = convertToJson(requestBody);
                
                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(augmentApiUrl + "/v1/test-results"))
                        .header("Content-Type", "application/json")
                        .header("Authorization", "Bearer " + apiKey)
                        .timeout(Duration.ofSeconds(30))
                        .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
                        .build();
                
                HttpResponse<String> response = httpClient.send(request, 
                        HttpResponse.BodyHandlers.ofString());
                
                if (response.statusCode() == 200) {
                    logger.info("✅ Test results sent to Augment successfully");
                } else {
                    logger.warn("⚠️ Failed to send test results: {}", response.statusCode());
                }
                
            } catch (Exception e) {
                logger.error("❌ Failed to send test results to Augment: {}", e.getMessage());
            }
        });
    }
    
    /**
     * Check if Augment integration is enabled and configured
     */
    public boolean isAugmentEnabled() {
        return !apiKey.isEmpty() && !augmentApiUrl.isEmpty();
    }
    
    /**
     * Test connection to Augment API
     */
    public CompletableFuture<Boolean> testConnection() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("🔗 Testing connection to Augment API");
                
                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(augmentApiUrl + "/v1/health"))
                        .header("Authorization", "Bearer " + apiKey)
                        .timeout(Duration.ofSeconds(10))
                        .GET()
                        .build();
                
                HttpResponse<String> response = httpClient.send(request, 
                        HttpResponse.BodyHandlers.ofString());
                
                boolean isConnected = response.statusCode() == 200;
                if (isConnected) {
                    logger.info("✅ Successfully connected to Augment API");
                } else {
                    logger.warn("⚠️ Augment API connection failed: {}", response.statusCode());
                }
                
                return isConnected;
                
            } catch (Exception e) {
                logger.error("❌ Failed to connect to Augment API: {}", e.getMessage());
                return false;
            }
        });
    }
    
    // Helper methods
    private String convertToJson(Map<String, Object> data) {
        // Simple JSON conversion - in production, use Jackson or Gson
        StringBuilder json = new StringBuilder("{");
        data.forEach((key, value) -> {
            json.append("\"").append(key).append("\":\"").append(value).append("\",");
        });
        if (json.length() > 1) {
            json.setLength(json.length() - 1); // Remove last comma
        }
        json.append("}");
        return json.toString();
    }
    
    private AugmentTestResponse parseAugmentResponse(String responseBody) {
        // Parse JSON response - implement based on actual Augment API response format
        logger.info("📝 Parsing Augment test response");
        return new AugmentTestResponse(responseBody);
    }
    
    private AugmentLocatorResponse parseLocatorResponse(String responseBody) {
        // Parse JSON response for locators
        logger.info("📝 Parsing Augment locator response");
        return new AugmentLocatorResponse(responseBody);
    }
    
    private AugmentTestResponse createFallbackResponse(String prompt, String baseUrl) {
        logger.info("🔄 Creating fallback test response");
        return AugmentTestResponse.createFallback(prompt, baseUrl);
    }
    
    private AugmentLocatorResponse createFallbackLocatorResponse(String elementDescription) {
        logger.info("🔄 Creating fallback locator response");
        return AugmentLocatorResponse.createFallback(elementDescription);
    }
}
