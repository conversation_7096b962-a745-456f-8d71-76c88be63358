package com.automation.generator;

import com.automation.models.TestCase;
import com.automation.models.TestStep;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Universal Prompt Processor - Handles ANY prompt from ANY module
 * Automatically generates test cases and manages prompt lifecycle
 */
public class UniversalPromptProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(UniversalPromptProcessor.class);
    
    // Directory paths
    private static final String PROMPTS_BASE_DIR = "src/test/resources/prompts";
    private static final String PENDING_DIR = PROMPTS_BASE_DIR + "/pending";
    private static final String PROCESSED_DIR = PROMPTS_BASE_DIR + "/processed";
    private static final String GENERATED_TESTS_DIR = PROMPTS_BASE_DIR + "/generated-tests";
    
    private final TestGenerator testGenerator;
    private final PromptProcessor promptProcessor;
    private final SimpleClassGenerator classGenerator;

    public UniversalPromptProcessor() {
        this.testGenerator = new TestGenerator();
        this.promptProcessor = new PromptProcessor();
        this.classGenerator = new SimpleClassGenerator();
        ensureDirectoriesExist();
    }
    
    /**
     * Process all pending prompt files and generate test cases
     */
    public List<TestCase> processAllPendingPrompts() {
        logger.info("🔍 Scanning for pending prompt files...");
        
        List<TestCase> allGeneratedTests = new ArrayList<>();
        
        try {
            List<Path> promptFiles = Files.walk(Paths.get(PENDING_DIR))
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".txt"))
                    .collect(Collectors.toList());
            
            logger.info("📋 Found {} prompt files to process", promptFiles.size());
            
            for (Path promptFile : promptFiles) {
                try {
                    List<TestCase> generatedTests = processPromptFile(promptFile);
                    allGeneratedTests.addAll(generatedTests);
                } catch (Exception e) {
                    logger.error("❌ Failed to process prompt file: {}", promptFile.getFileName(), e);
                }
            }
            
        } catch (IOException e) {
            logger.error("❌ Failed to scan prompt directory", e);
        }
        
        logger.info("✅ Generated {} test cases from all prompts", allGeneratedTests.size());
        return allGeneratedTests;
    }
    
    /**
     * Process a single prompt file
     */
    public List<TestCase> processPromptFile(Path promptFile) throws IOException {
        logger.info("📝 Processing prompt file: {}", promptFile.getFileName());
        
        // Read prompt content
        String promptContent = Files.readString(promptFile).trim();
        if (promptContent.isEmpty()) {
            logger.warn("⚠️ Empty prompt file: {}", promptFile.getFileName());
            return new ArrayList<>();
        }
        
        logger.info("📄 Prompt content: {}", promptContent);

        // Generate required classes if prompt contains help/support
        if (promptContent.toLowerCase().contains("help") || promptContent.toLowerCase().contains("support")) {
            logger.info("🏗️ Generating HelpAndSupport classes...");
            classGenerator.createHelpAndSupportClasses();
        }

        // Extract module/category from filename or content
        String moduleName = extractModuleName(promptFile, promptContent);
        String baseUrl = extractBaseUrl(promptContent);
        
        // Generate test cases
        List<TestCase> testCases = testGenerator.generateTestCases(promptContent, baseUrl);
        
        if (testCases.isEmpty()) {
            logger.warn("⚠️ No test cases generated for prompt: {}", promptFile.getFileName());
            return new ArrayList<>();
        }
        
        // Enhance test cases with module information
        for (TestCase testCase : testCases) {
            testCase.addTag(moduleName);
            testCase.addTag("auto-generated");
            testCase.setCategory(determineCategory(promptContent));
            testCase.setModule(moduleName);
        }
        
        // Save generated test cases
        saveGeneratedTestCases(testCases, promptFile.getFileName().toString());
        
        // Move prompt to processed directory
        moveToProcessed(promptFile);
        
        logger.info("✅ Successfully processed prompt: {} -> {} test cases", 
                   promptFile.getFileName(), testCases.size());
        
        return testCases;
    }
    
    /**
     * Extract module name from filename or content
     */
    private String extractModuleName(Path promptFile, String content) {
        String fileName = promptFile.getFileName().toString().replace(".txt", "");
        
        // Try to extract from filename first
        if (fileName.contains("_")) {
            return fileName.split("_")[0];
        }
        
        // Try to extract from content
        String lowerContent = content.toLowerCase();
        if (lowerContent.contains("jeevansathi")) return "jeevansathi";
        if (lowerContent.contains("login")) return "authentication";
        if (lowerContent.contains("search")) return "search";
        if (lowerContent.contains("profile")) return "profile";
        if (lowerContent.contains("register")) return "registration";
        if (lowerContent.contains("payment")) return "payment";
        if (lowerContent.contains("message")) return "messaging";
        if (lowerContent.contains("match")) return "matching";
        
        // Default to generic
        return "general";
    }
    
    /**
     * Extract base URL from content or use default
     */
    private String extractBaseUrl(String content) {
        String lowerContent = content.toLowerCase();
        
        // Look for specific URLs in content
        if (lowerContent.contains("jeevansathi")) {
            return "https://www.jeevansathi.com/";
        }
        if (lowerContent.contains("shaadi")) {
            return "https://www.shaadi.com/";
        }
        if (lowerContent.contains("bharatmatrimony")) {
            return "https://www.bharatmatrimony.com/";
        }
        
        // Extract URL pattern if present
        String[] words = content.split("\\s+");
        for (String word : words) {
            if (word.startsWith("http://") || word.startsWith("https://")) {
                return word;
            }
        }
        
        // Default URL
        return "https://www.jeevansathi.com/";
    }
    
    /**
     * Determine test category from content
     */
    private String determineCategory(String content) {
        String lowerContent = content.toLowerCase();
        
        if (lowerContent.contains("smoke") || lowerContent.contains("basic") || 
            lowerContent.contains("navigation")) {
            return "smoke";
        }
        if (lowerContent.contains("regression") || lowerContent.contains("full")) {
            return "regression";
        }
        if (lowerContent.contains("integration") || lowerContent.contains("api")) {
            return "integration";
        }
        if (lowerContent.contains("performance") || lowerContent.contains("load")) {
            return "performance";
        }
        
        return "functional";
    }
    
    /**
     * Save generated test cases to files
     */
    private void saveGeneratedTestCases(List<TestCase> testCases, String originalFileName) {
        try {
            String baseFileName = originalFileName.replace(".txt", "");
            
            for (int i = 0; i < testCases.size(); i++) {
                TestCase testCase = testCases.get(i);
                String testFileName = String.format("%s_test_%d.json", baseFileName, i + 1);
                Path testFilePath = Paths.get(GENERATED_TESTS_DIR, testFileName);
                
                // Convert test case to JSON and save
                String testCaseJson = convertTestCaseToJson(testCase);
                Files.writeString(testFilePath, testCaseJson);
                
                logger.info("💾 Saved generated test: {}", testFileName);
            }
            
        } catch (IOException e) {
            logger.error("❌ Failed to save generated test cases", e);
        }
    }
    
    /**
     * Convert test case to JSON format
     */
    private String convertTestCaseToJson(TestCase testCase) {
        StringBuilder json = new StringBuilder();
        json.append("{\n");
        json.append("  \"id\": \"").append(testCase.getId()).append("\",\n");
        json.append("  \"name\": \"").append(testCase.getName()).append("\",\n");
        json.append("  \"description\": \"").append(testCase.getDescription()).append("\",\n");
        json.append("  \"module\": \"").append(testCase.getModule()).append("\",\n");
        json.append("  \"category\": \"").append(testCase.getCategory()).append("\",\n");
        json.append("  \"priority\": \"").append(testCase.getPriority()).append("\",\n");
        json.append("  \"url\": \"").append(testCase.getUrl()).append("\",\n");
        json.append("  \"tags\": [");
        
        List<String> tags = testCase.getTags();
        for (int i = 0; i < tags.size(); i++) {
            json.append("\"").append(tags.get(i)).append("\"");
            if (i < tags.size() - 1) json.append(", ");
        }
        json.append("],\n");
        
        json.append("  \"steps\": [\n");
        List<TestStep> steps = testCase.getSteps();
        for (int i = 0; i < steps.size(); i++) {
            TestStep step = steps.get(i);
            json.append("    {\n");
            json.append("      \"stepNumber\": ").append(step.getStepNumber()).append(",\n");
            json.append("      \"action\": \"").append(step.getAction()).append("\",\n");
            json.append("      \"description\": \"").append(step.getDescription()).append("\",\n");
            if (step.getLocator() != null) {
                json.append("      \"locator\": \"").append(step.getLocator()).append("\",\n");
            }
            if (step.getInputData() != null) {
                json.append("      \"inputData\": \"").append(step.getInputData()).append("\",\n");
            }
            json.append("      \"expectedResult\": \"").append(step.getExpectedResult()).append("\"\n");
            json.append("    }");
            if (i < steps.size() - 1) json.append(",");
            json.append("\n");
        }
        json.append("  ],\n");
        
        json.append("  \"expectedResult\": \"").append(testCase.getExpectedResult()).append("\",\n");
        json.append("  \"estimatedDuration\": ").append(testCase.getEstimatedDuration()).append(",\n");
        json.append("  \"createdBy\": \"").append(testCase.getCreatedBy()).append("\",\n");
        json.append("  \"createdAt\": \"").append(new Date()).append("\"\n");
        json.append("}");
        
        return json.toString();
    }
    
    /**
     * Move processed prompt to processed directory
     */
    private void moveToProcessed(Path promptFile) {
        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String fileName = promptFile.getFileName().toString();
            String processedFileName = timestamp + "_" + fileName;
            
            Path processedPath = Paths.get(PROCESSED_DIR, processedFileName);
            Files.move(promptFile, processedPath);
            
            logger.info("📁 Moved prompt to processed: {}", processedFileName);
            
        } catch (IOException e) {
            logger.error("❌ Failed to move prompt file to processed directory", e);
        }
    }
    
    /**
     * Create a new prompt file
     */
    public void createPromptFile(String fileName, String promptContent) {
        try {
            if (!fileName.endsWith(".txt")) {
                fileName += ".txt";
            }
            
            Path promptPath = Paths.get(PENDING_DIR, fileName);
            Files.writeString(promptPath, promptContent);
            
            logger.info("📝 Created new prompt file: {}", fileName);
            
        } catch (IOException e) {
            logger.error("❌ Failed to create prompt file: {}", fileName, e);
        }
    }
    
    /**
     * List all pending prompt files
     */
    public List<String> listPendingPrompts() {
        try {
            return Files.walk(Paths.get(PENDING_DIR))
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".txt"))
                    .map(path -> path.getFileName().toString())
                    .collect(Collectors.toList());
        } catch (IOException e) {
            logger.error("❌ Failed to list pending prompts", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * Get statistics about prompt processing
     */
    public Map<String, Integer> getProcessingStats() {
        Map<String, Integer> stats = new HashMap<>();
        
        try {
            int pendingCount = (int) Files.walk(Paths.get(PENDING_DIR))
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".txt"))
                    .count();
            
            int processedCount = (int) Files.walk(Paths.get(PROCESSED_DIR))
                    .filter(Files::isRegularFile)
                    .count();
            
            int generatedCount = (int) Files.walk(Paths.get(GENERATED_TESTS_DIR))
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".json"))
                    .count();
            
            stats.put("pending", pendingCount);
            stats.put("processed", processedCount);
            stats.put("generated", generatedCount);
            
        } catch (IOException e) {
            logger.error("❌ Failed to get processing stats", e);
        }
        
        return stats;
    }
    
    /**
     * Ensure all required directories exist
     */
    private void ensureDirectoriesExist() {
        try {
            Files.createDirectories(Paths.get(PENDING_DIR));
            Files.createDirectories(Paths.get(PROCESSED_DIR));
            Files.createDirectories(Paths.get(GENERATED_TESTS_DIR));
        } catch (IOException e) {
            logger.error("❌ Failed to create prompt directories", e);
        }
    }
}
