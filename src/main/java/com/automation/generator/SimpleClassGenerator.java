package com.automation.generator;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Simple Class Generator - Creates Logic, Verification, and Page Object classes
 * based on prompt analysis
 */
public class SimpleClassGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(SimpleClassGenerator.class);
    
    /**
     * Analyze prompt and create HelpAndSupport classes
     */
    public void createHelpAndSupportClasses() {
        try {
            createHelpAndSupportLogic();
            createHelpAndSupportVerification();
            createHelpAndSupportPage();
            logger.info("✅ Created HelpAndSupport classes successfully");
        } catch (IOException e) {
            logger.error("❌ Failed to create HelpAndSupport classes", e);
        }
    }
    
    /**
     * Create HelpAndSupportLogic class
     */
    private void createHelpAndSupportLogic() throws IOException {
        String className = "HelpAndSupportLogic";
        Path classPath = Paths.get("src/test/java/com/automation/logic", className + ".java");
        
        if (Files.exists(classPath)) {
            logger.info("📄 Logic class already exists: {}", className);
            return;
        }
        
        String content = "package com.automation.logic;\n\n" +
                "import com.automation.core.TestBase;\n" +
                "import com.automation.pages.HelpAndSupportPage;\n" +
                "import com.automation.verification.HelpAndSupportVerification;\n" +
                "import com.automation.utils.ActionUtility;\n" +
                "import org.testng.annotations.Test;\n" +
                "import org.slf4j.Logger;\n" +
                "import org.slf4j.LoggerFactory;\n\n" +
                "/**\n" +
                " * HelpAndSupport Logic Class - Business logic and test methods\n" +
                " */\n" +
                "public class HelpAndSupportLogic extends TestBase {\n\n" +
                "    private static final Logger logger = LoggerFactory.getLogger(HelpAndSupportLogic.class);\n\n" +
                "    private final HelpAndSupportVerification verification = new HelpAndSupportVerification();\n" +
                "    private HelpAndSupportPage helpAndSupportPage;\n" +
                "    private ActionUtility actionUtility;\n\n" +
                "    private void initializePageObjects() {\n" +
                "        if (helpAndSupportPage == null) {\n" +
                "            helpAndSupportPage = new HelpAndSupportPage(getPage());\n" +
                "        }\n" +
                "        if (actionUtility == null) {\n" +
                "            actionUtility = new ActionUtility(getPage());\n" +
                "        }\n" +
                "    }\n\n" +
                "    @Test(description = \"Help and Support Test\",\n" +
                "          groups = {\"help\", \"support\", \"functional\"}, priority = 1)\n" +
                "    public void testHelpAndSupportFunctionality() {\n\n" +
                "        logTestInfo(\"🚀 Starting Help and Support test\");\n\n" +
                "        try {\n" +
                "            initializePageObjects();\n\n" +
                "            // Navigate to Jeevansathi\n" +
                "            actionUtility.navigateTo(\"https://www.jeevansathi.com/\");\n" +
                "            actionUtility.waitFor(3000);\n\n" +
                "            // Click on help button\n" +
                "            boolean helpClicked = helpAndSupportPage.clickHelpButton();\n" +
                "            verification.verifyHelpButtonClicked(helpClicked);\n\n" +
                "            // Verify if all categories getting displayed\n" +
                "            boolean categoriesDisplayed = helpAndSupportPage.verifyCategoriesDisplayed();\n" +
                "            verification.verifyCategoriesDisplayed(categoriesDisplayed);\n\n" +
                "            logTestPass(\"✅ Help and Support test completed successfully!\");\n\n" +
                "        } catch (Exception e) {\n" +
                "            logTestFail(\"❌ Help and Support test failed: \" + e.getMessage());\n" +
                "            actionUtility.takeScreenshot(\"help_support_test_failure\");\n" +
                "            throw e;\n" +
                "        }\n" +
                "    }\n" +
                "}\n";
        
        Files.createDirectories(classPath.getParent());
        Files.writeString(classPath, content);
        logger.info("✅ Created Logic class: {}", classPath);
    }
    
    /**
     * Create HelpAndSupportVerification class
     */
    private void createHelpAndSupportVerification() throws IOException {
        String className = "HelpAndSupportVerification";
        Path classPath = Paths.get("src/test/java/com/automation/verification", className + ".java");
        
        if (Files.exists(classPath)) {
            logger.info("📄 Verification class already exists: {}", className);
            return;
        }
        
        String content = "package com.automation.verification;\n\n" +
                "import com.automation.core.TestBase;\n" +
                "import org.slf4j.Logger;\n" +
                "import org.slf4j.LoggerFactory;\n\n" +
                "/**\n" +
                " * HelpAndSupport Verification Class - All verification logic\n" +
                " */\n" +
                "public class HelpAndSupportVerification extends TestBase {\n\n" +
                "    private static final Logger logger = LoggerFactory.getLogger(HelpAndSupportVerification.class);\n\n" +
                "    public void verifyHelpButtonClicked(boolean clicked) {\n" +
                "        if (clicked) {\n" +
                "            logTestPass(\"✅ Help button clicked successfully\");\n" +
                "        } else {\n" +
                "            logTestFail(\"❌ Help button click failed\");\n" +
                "        }\n" +
                "    }\n\n" +
                "    public void verifyCategoriesDisplayed(boolean displayed) {\n" +
                "        if (displayed) {\n" +
                "            logTestPass(\"✅ All categories are displayed\");\n" +
                "        } else {\n" +
                "            logTestFail(\"❌ Categories are not displayed properly\");\n" +
                "        }\n" +
                "    }\n\n" +
                "    public void verifyElementPresent(boolean isPresent, String elementName) {\n" +
                "        if (isPresent) {\n" +
                "            logTestPass(\"✅ \" + elementName + \" is present\");\n" +
                "        } else {\n" +
                "            logTestFail(\"❌ \" + elementName + \" is not present\");\n" +
                "        }\n" +
                "    }\n" +
                "}\n";
        
        Files.createDirectories(classPath.getParent());
        Files.writeString(classPath, content);
        logger.info("✅ Created Verification class: {}", classPath);
    }
    
    /**
     * Create HelpAndSupportPage class
     */
    private void createHelpAndSupportPage() throws IOException {
        String className = "HelpAndSupportPage";
        Path classPath = Paths.get("src/main/java/com/automation/pages", className + ".java");
        
        if (Files.exists(classPath)) {
            logger.info("📄 Page class already exists: {}", className);
            return;
        }
        
        String content = "package com.automation.pages;\n\n" +
                "import com.microsoft.playwright.Page;\n" +
                "import org.slf4j.Logger;\n" +
                "import org.slf4j.LoggerFactory;\n\n" +
                "/**\n" +
                " * HelpAndSupport Page Object - Page interactions\n" +
                " */\n" +
                "public class HelpAndSupportPage extends BasePage {\n\n" +
                "    private static final Logger logger = LoggerFactory.getLogger(HelpAndSupportPage.class);\n\n" +
                "    // Page elements\n" +
                "    private static final String HELP_BUTTON = \"button:has-text('Help')\";\n" +
                "    private static final String HELP_LINK = \"a:has-text('Help')\";\n" +
                "    private static final String SUPPORT_LINK = \"a:has-text('Support')\";\n" +
                "    private static final String CATEGORIES_SECTION = \".help-categories\";\n" +
                "    private static final String CATEGORY_ITEMS = \".category-item\";\n\n" +
                "    public HelpAndSupportPage(Page page) {\n" +
                "        super(page);\n" +
                "    }\n\n" +
                "    public boolean clickHelpButton() {\n" +
                "        logger.info(\"🖱️ Clicking Help button\");\n\n" +
                "        String[] selectors = {\n" +
                "            HELP_BUTTON,\n" +
                "            HELP_LINK,\n" +
                "            SUPPORT_LINK,\n" +
                "            \"text=/help/i\",\n" +
                "            \"text=/support/i\"\n" +
                "        };\n\n" +
                "        for (String selector : selectors) {\n" +
                "            if (actionUtility.clickElement(selector)) {\n" +
                "                logger.info(\"✅ Successfully clicked help element\");\n" +
                "                actionUtility.waitFor(2000);\n" +
                "                return true;\n" +
                "            }\n" +
                "        }\n\n" +
                "        logger.warn(\"⚠️ Failed to click help button\");\n" +
                "        return false;\n" +
                "    }\n\n" +
                "    public boolean verifyCategoriesDisplayed() {\n" +
                "        logger.info(\"🔍 Verifying categories are displayed\");\n\n" +
                "        // Check for categories section\n" +
                "        if (actionUtility.isElementPresent(CATEGORIES_SECTION)) {\n" +
                "            logger.info(\"✅ Categories section found\");\n" +
                "            return true;\n" +
                "        }\n\n" +
                "        // Check for individual category items\n" +
                "        int categoryCount = actionUtility.getElementCount(CATEGORY_ITEMS);\n" +
                "        if (categoryCount > 0) {\n" +
                "            logger.info(\"✅ Found {} category items\", categoryCount);\n" +
                "            return true;\n" +
                "        }\n\n" +
                "        // Generic check for any help-related content\n" +
                "        String[] fallbackSelectors = {\n" +
                "            \".help-content\",\n" +
                "            \".support-content\",\n" +
                "            \"[class*='help']\",\n" +
                "            \"[class*='support']\",\n" +
                "            \"text=/faq/i\",\n" +
                "            \"text=/contact/i\"\n" +
                "        };\n\n" +
                "        for (String selector : fallbackSelectors) {\n" +
                "            if (actionUtility.isElementPresent(selector)) {\n" +
                "                logger.info(\"✅ Found help content with selector: {}\", selector);\n" +
                "                return true;\n" +
                "            }\n" +
                "        }\n\n" +
                "        logger.warn(\"⚠️ No categories or help content found\");\n" +
                "        return false;\n" +
                "    }\n\n" +
                "    public boolean isHelpPageLoaded() {\n" +
                "        return actionUtility.getPageTitle().toLowerCase().contains(\"help\") ||\n" +
                "               actionUtility.isElementPresent(HELP_BUTTON) ||\n" +
                "               actionUtility.isElementPresent(CATEGORIES_SECTION);\n" +
                "    }\n" +
                "}\n";
        
        Files.createDirectories(classPath.getParent());
        Files.writeString(classPath, content);
        logger.info("✅ Created Page class: {}", classPath);
    }
}
