package com.automation.generator;

import com.automation.config.ConfigManager;
import com.automation.models.TestCase;
import com.automation.models.TestStep;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * AI-powered Test Generator using OpenAI/LangChain
 * Generates test cases from natural language prompts
 */
public class TestGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(TestGenerator.class);
    private final ConfigManager config;
    private final PromptProcessor promptProcessor;
    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;
    
    public TestGenerator() {
        this.config = ConfigManager.getInstance();
        this.promptProcessor = new PromptProcessor();
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(30))
                .build();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Generate test cases from a natural language prompt
     */
    public List<TestCase> generateTestCases(String prompt, String baseUrl) {
        if (!config.isTestGenerationEnabled()) {
            logger.warn("Test generation is disabled in configuration");
            return generateFallbackTestCase(prompt, baseUrl);
        }
        
        logger.info("Generating test cases for prompt: {}", prompt);
        
        try {
            // Try AI-powered generation first
            List<TestCase> aiGeneratedTests = generateWithAI(prompt, baseUrl);
            if (!aiGeneratedTests.isEmpty()) {
                return aiGeneratedTests;
            }
        } catch (Exception e) {
            logger.warn("AI generation failed, falling back to rule-based generation: {}", e.getMessage());
        }
        
        // Fallback to rule-based generation
        return generateFallbackTestCase(prompt, baseUrl);
    }
    
    /**
     * Generate test cases using AI (OpenAI/LangChain)
     */
    private List<TestCase> generateWithAI(String prompt, String baseUrl) throws Exception {
        String apiKey = config.getOpenAiApiKey();
        if (apiKey == null || apiKey.trim().isEmpty()) {
            logger.warn("OpenAI API key not configured, skipping AI generation");
            throw new IllegalStateException("OpenAI API key not configured");
        }
        
        // Prepare the AI prompt
        String aiPrompt = buildAIPrompt(prompt, baseUrl);
        
        // Call OpenAI API
        String response = callOpenAI(aiPrompt, apiKey);
        
        // Parse the response and create test cases
        return parseAIResponse(response, baseUrl);
    }
    
    /**
     * Build AI prompt for test generation
     */
    private String buildAIPrompt(String userPrompt, String baseUrl) {
        return String.format(
            "You are an expert test automation engineer. Generate detailed test cases for web automation using Playwright.\n\n" +
            "User Request: %s\n" +
            "Base URL: %s\n\n" +
            "Please generate test cases in the following JSON format:\n" +
            "{\n" +
            "  \"testCases\": [\n" +
            "    {\n" +
            "      \"name\": \"descriptive test name\",\n" +
            "      \"description\": \"detailed description\",\n" +
            "      \"priority\": \"HIGH|MEDIUM|LOW\",\n" +
            "      \"category\": \"smoke|regression|functional\",\n" +
            "      \"steps\": [\n" +
            "        {\n" +
            "          \"stepNumber\": 1,\n" +
            "          \"action\": \"NAVIGATE|CLICK|TYPE|SELECT|VERIFY|WAIT\",\n" +
            "          \"description\": \"step description\",\n" +
            "          \"locator\": \"CSS selector or XPath\",\n" +
            "          \"inputData\": \"data to input (if applicable)\",\n" +
            "          \"expectedResult\": \"expected outcome\"\n" +
            "        }\n" +
            "      ],\n" +
            "      \"expectedResult\": \"overall test expected result\",\n" +
            "      \"preconditions\": \"any setup required\",\n" +
            "      \"postconditions\": \"cleanup required\"\n" +
            "    }\n" +
            "  ]\n" +
            "}\n\n" +
            "Guidelines:\n" +
            "- Use realistic CSS selectors and XPath expressions\n" +
            "- Include proper verification steps\n" +
            "- Consider edge cases and error scenarios\n" +
            "- Make steps atomic and clear\n" +
            "- Include wait steps where necessary\n" +
            "- Use Playwright-compatible locators\n\n" +
            "Generate 1-3 comprehensive test cases based on the user request.",
            userPrompt, baseUrl);
    }
    
    /**
     * Call OpenAI API
     */
    private String callOpenAI(String prompt, String apiKey) throws Exception {
        String requestBody = objectMapper.writeValueAsString(new OpenAIRequest(
            config.getTestGenerationModel(),
            prompt,
            config.getTestGenerationMaxTokens(),
            config.getTestGenerationTemperature()
        ));
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("https://api.openai.com/v1/chat/completions"))
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + apiKey)
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .timeout(Duration.ofSeconds(60))
                .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() != 200) {
            throw new RuntimeException("OpenAI API call failed with status: " + response.statusCode() + 
                                     ", body: " + response.body());
        }
        
        return response.body();
    }
    
    /**
     * Parse AI response and create test cases
     */
    private List<TestCase> parseAIResponse(String response, String baseUrl) throws Exception {
        JsonNode responseNode = objectMapper.readTree(response);
        JsonNode choicesNode = responseNode.get("choices");
        
        if (choicesNode == null || choicesNode.size() == 0) {
            throw new RuntimeException("No choices in OpenAI response");
        }
        
        String content = choicesNode.get(0).get("message").get("content").asText();
        
        // Extract JSON from the content (it might be wrapped in markdown)
        String jsonContent = extractJsonFromContent(content);
        
        JsonNode testCasesNode = objectMapper.readTree(jsonContent);
        JsonNode testCasesArray = testCasesNode.get("testCases");
        
        List<TestCase> testCases = new ArrayList<>();
        
        if (testCasesArray != null && testCasesArray.isArray()) {
            for (JsonNode testCaseNode : testCasesArray) {
                TestCase testCase = parseTestCaseFromJson(testCaseNode, baseUrl);
                testCases.add(testCase);
            }
        }
        
        logger.info("Successfully parsed {} test cases from AI response", testCases.size());
        return testCases;
    }
    
    /**
     * Extract JSON content from AI response (remove markdown formatting)
     */
    private String extractJsonFromContent(String content) {
        // Remove markdown code blocks
        content = content.replaceAll("```json\\s*", "").replaceAll("```\\s*", "");
        
        // Find the JSON object
        int startIndex = content.indexOf("{");
        int endIndex = content.lastIndexOf("}") + 1;
        
        if (startIndex >= 0 && endIndex > startIndex) {
            return content.substring(startIndex, endIndex);
        }
        
        return content;
    }
    
    /**
     * Parse test case from JSON node
     */
    private TestCase parseTestCaseFromJson(JsonNode testCaseNode, String baseUrl) {
        TestCase testCase = new TestCase();
        
        testCase.setId(UUID.randomUUID().toString());
        testCase.setName(testCaseNode.get("name").asText());
        testCase.setDescription(testCaseNode.get("description").asText());
        testCase.setPriority(testCaseNode.has("priority") ? testCaseNode.get("priority").asText() : "MEDIUM");
        testCase.setCategory(testCaseNode.has("category") ? testCaseNode.get("category").asText() : "functional");
        testCase.setUrl(baseUrl);
        testCase.addTag("ai-generated");
        testCase.setCreatedBy("AI Generator");
        
        if (testCaseNode.has("expectedResult")) {
            testCase.setExpectedResult(testCaseNode.get("expectedResult").asText());
        }
        
        if (testCaseNode.has("preconditions")) {
            testCase.setPreconditions(testCaseNode.get("preconditions").asText());
        }
        
        if (testCaseNode.has("postconditions")) {
            testCase.setPostconditions(testCaseNode.get("postconditions").asText());
        }
        
        // Parse steps
        JsonNode stepsNode = testCaseNode.get("steps");
        if (stepsNode != null && stepsNode.isArray()) {
            List<TestStep> steps = new ArrayList<>();
            for (JsonNode stepNode : stepsNode) {
                TestStep step = parseTestStepFromJson(stepNode);
                steps.add(step);
            }
            testCase.setSteps(steps);
        }
        
        // Set estimated duration
        testCase.setEstimatedDuration(testCase.getStepCount() * 15); // 15 seconds per step
        
        return testCase;
    }
    
    /**
     * Parse test step from JSON node
     */
    private TestStep parseTestStepFromJson(JsonNode stepNode) {
        TestStep step = new TestStep();
        
        step.setId(UUID.randomUUID().toString());
        step.setStepNumber(stepNode.get("stepNumber").asInt());
        step.setAction(stepNode.get("action").asText());
        step.setDescription(stepNode.get("description").asText());
        
        if (stepNode.has("locator")) {
            step.setLocator(stepNode.get("locator").asText());
        }
        
        if (stepNode.has("inputData")) {
            step.setInputData(stepNode.get("inputData").asText());
        }
        
        if (stepNode.has("expectedResult")) {
            step.setExpectedResult(stepNode.get("expectedResult").asText());
        }
        
        return step;
    }
    
    /**
     * Generate fallback test case using rule-based approach
     */
    private List<TestCase> generateFallbackTestCase(String prompt, String baseUrl) {
        logger.info("Generating fallback test case using rule-based approach");
        
        TestCase testCase = promptProcessor.createTestCaseFromPrompt(prompt, baseUrl);
        testCase.setId(UUID.randomUUID().toString());
        testCase.addTag("rule-based");
        
        List<TestCase> testCases = new ArrayList<>();
        testCases.add(testCase);
        
        logger.info("Generated fallback test case: {}", testCase.getName());
        return testCases;
    }
    
    /**
     * OpenAI API request model
     */
    private static class OpenAIRequest {
        public String model;
        public List<Message> messages;
        public int max_tokens;
        public double temperature;
        
        public OpenAIRequest(String model, String prompt, int maxTokens, double temperature) {
            this.model = model;
            this.messages = List.of(new Message("user", prompt));
            this.max_tokens = maxTokens;
            this.temperature = temperature;
        }
        
        public static class Message {
            public String role;
            public String content;
            
            public Message(String role, String content) {
                this.role = role;
                this.content = content;
            }
        }
    }
}
