package com.automation.generator;

import com.automation.models.TestCase;
import com.automation.models.TestStep;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Prompt Processor for converting natural language prompts into test steps
 * Handles parsing and interpretation of user prompts for test generation
 */
public class PromptProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(PromptProcessor.class);
    
    // Common action patterns
    private static final Pattern NAVIGATE_PATTERN = Pattern.compile(
        "(?i)(?:navigate|go|visit|open)\\s+(?:to\\s+)?[\"']?([^\"'\\s]+)[\"']?", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern CLICK_PATTERN = Pattern.compile(
        "(?i)click\\s+(?:on\\s+)?(?:the\\s+)?[\"']?([^\"']+)[\"']?", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern TYPE_PATTERN = Pattern.compile(
        "(?i)(?:type|enter|input|fill)\\s+[\"']([^\"']+)[\"']\\s+(?:in|into|to)\\s+(?:the\\s+)?[\"']?([^\"']+)[\"']?", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern SELECT_PATTERN = Pattern.compile(
        "(?i)select\\s+[\"']([^\"']+)[\"']\\s+(?:from|in)\\s+(?:the\\s+)?[\"']?([^\"']+)[\"']?", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern VERIFY_PATTERN = Pattern.compile(
        "(?i)(?:verify|check|assert|ensure)\\s+(?:that\\s+)?[\"']?([^\"']+)[\"']?", 
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern WAIT_PATTERN = Pattern.compile(
        "(?i)wait\\s+(?:for\\s+)?(?:(\\d+)\\s+(?:seconds?|ms|milliseconds?)\\s+)?(?:for\\s+)?[\"']?([^\"']+)?[\"']?", 
        Pattern.CASE_INSENSITIVE
    );
    
    /**
     * Process a natural language prompt and convert it to test steps
     */
    public List<TestStep> processPrompt(String prompt) {
        if (prompt == null || prompt.trim().isEmpty()) {
            logger.warn("Empty prompt provided");
            return new ArrayList<>();
        }
        
        logger.info("Processing prompt: {}", prompt);
        
        List<TestStep> steps = new ArrayList<>();
        
        // Split prompt into sentences
        String[] sentences = splitIntoSentences(prompt);
        
        int stepNumber = 1;
        for (String sentence : sentences) {
            TestStep step = processSentence(sentence.trim(), stepNumber);
            if (step != null) {
                steps.add(step);
                stepNumber++;
            }
        }
        
        logger.info("Generated {} test steps from prompt", steps.size());
        return steps;
    }
    
    /**
     * Process a single sentence and convert it to a test step
     */
    private TestStep processSentence(String sentence, int stepNumber) {
        if (sentence.isEmpty()) {
            return null;
        }
        
        logger.debug("Processing sentence: {}", sentence);
        
        // Try to match different action patterns
        TestStep step = null;
        
        // Navigation
        step = processNavigationAction(sentence, stepNumber);
        if (step != null) return step;
        
        // Click actions
        step = processClickAction(sentence, stepNumber);
        if (step != null) return step;
        
        // Type/Input actions
        step = processTypeAction(sentence, stepNumber);
        if (step != null) return step;
        
        // Select actions
        step = processSelectAction(sentence, stepNumber);
        if (step != null) return step;
        
        // Verification actions
        step = processVerifyAction(sentence, stepNumber);
        if (step != null) return step;
        
        // Wait actions
        step = processWaitAction(sentence, stepNumber);
        if (step != null) return step;
        
        // If no specific pattern matches, create a generic step
        return createGenericStep(sentence, stepNumber);
    }
    
    /**
     * Process navigation actions
     */
    private TestStep processNavigationAction(String sentence, int stepNumber) {
        Matcher matcher = NAVIGATE_PATTERN.matcher(sentence);
        if (matcher.find()) {
            String url = matcher.group(1);
            TestStep step = new TestStep(stepNumber, TestStep.ACTION_NAVIGATE, 
                "Navigate to " + url);
            step.setInputData(url);
            step.setExpectedResult("Page should load successfully");
            return step;
        }
        return null;
    }
    
    /**
     * Process click actions
     */
    private TestStep processClickAction(String sentence, int stepNumber) {
        Matcher matcher = CLICK_PATTERN.matcher(sentence);
        if (matcher.find()) {
            String element = matcher.group(1);
            TestStep step = new TestStep(stepNumber, TestStep.ACTION_CLICK, 
                "Click on " + element);
            step.setLocator(generateLocator(element));
            step.setExpectedResult("Element should be clicked successfully");
            return step;
        }
        return null;
    }
    
    /**
     * Process type/input actions
     */
    private TestStep processTypeAction(String sentence, int stepNumber) {
        Matcher matcher = TYPE_PATTERN.matcher(sentence);
        if (matcher.find()) {
            String text = matcher.group(1);
            String element = matcher.group(2);
            TestStep step = new TestStep(stepNumber, TestStep.ACTION_TYPE, 
                "Type '" + text + "' in " + element);
            step.setInputData(text);
            step.setLocator(generateLocator(element));
            step.setExpectedResult("Text should be entered successfully");
            return step;
        }
        return null;
    }
    
    /**
     * Process select actions
     */
    private TestStep processSelectAction(String sentence, int stepNumber) {
        Matcher matcher = SELECT_PATTERN.matcher(sentence);
        if (matcher.find()) {
            String option = matcher.group(1);
            String element = matcher.group(2);
            TestStep step = new TestStep(stepNumber, TestStep.ACTION_SELECT, 
                "Select '" + option + "' from " + element);
            step.setInputData(option);
            step.setLocator(generateLocator(element));
            step.setExpectedResult("Option should be selected successfully");
            return step;
        }
        return null;
    }
    
    /**
     * Process verification actions
     */
    private TestStep processVerifyAction(String sentence, int stepNumber) {
        Matcher matcher = VERIFY_PATTERN.matcher(sentence);
        if (matcher.find()) {
            String condition = matcher.group(1);
            TestStep step = new TestStep(stepNumber, TestStep.ACTION_VERIFY, 
                "Verify " + condition);
            step.setExpectedResult(condition);
            return step;
        }
        return null;
    }
    
    /**
     * Process wait actions
     */
    private TestStep processWaitAction(String sentence, int stepNumber) {
        Matcher matcher = WAIT_PATTERN.matcher(sentence);
        if (matcher.find()) {
            String duration = matcher.group(1);
            String condition = matcher.group(2);
            
            String description = "Wait";
            if (duration != null) {
                description += " for " + duration + " seconds";
            }
            if (condition != null && !condition.isEmpty()) {
                description += " for " + condition;
            }
            
            TestStep step = new TestStep(stepNumber, TestStep.ACTION_WAIT, description);
            if (duration != null) {
                step.setInputData(duration);
            }
            if (condition != null) {
                step.setExpectedResult(condition);
            }
            return step;
        }
        return null;
    }
    
    /**
     * Create a generic step for unrecognized patterns
     */
    private TestStep createGenericStep(String sentence, int stepNumber) {
        TestStep step = new TestStep(stepNumber, "MANUAL", sentence);
        step.setExpectedResult("Step should be completed manually");
        logger.debug("Created generic step for: {}", sentence);
        return step;
    }
    
    /**
     * Generate a locator based on element description
     */
    private String generateLocator(String elementDescription) {
        String desc = elementDescription.toLowerCase().trim();
        
        // Common element patterns
        if (desc.contains("button")) {
            return "button:has-text(\"" + extractElementText(elementDescription) + "\")";
        } else if (desc.contains("link")) {
            return "a:has-text(\"" + extractElementText(elementDescription) + "\")";
        } else if (desc.contains("input") || desc.contains("field") || desc.contains("textbox")) {
            return "input[placeholder*=\"" + extractElementText(elementDescription) + "\"]";
        } else if (desc.contains("dropdown") || desc.contains("select")) {
            return "select";
        } else if (desc.contains("checkbox")) {
            return "input[type=\"checkbox\"]";
        } else if (desc.contains("radio")) {
            return "input[type=\"radio\"]";
        } else {
            // Generic locator based on text content
            return "*:has-text(\"" + extractElementText(elementDescription) + "\")";
        }
    }
    
    /**
     * Extract meaningful text from element description
     */
    private String extractElementText(String elementDescription) {
        // Remove common words and return the meaningful part
        String text = elementDescription.replaceAll("(?i)\\b(button|link|input|field|textbox|dropdown|select|checkbox|radio|the|a|an)\\b", "")
                                      .trim()
                                      .replaceAll("\\s+", " ");
        return text.isEmpty() ? elementDescription : text;
    }
    
    /**
     * Split prompt into sentences
     */
    private String[] splitIntoSentences(String prompt) {
        // Split by common sentence delimiters
        return prompt.split("[.!?;]|\\band\\b|\\bthen\\b");
    }
    
    /**
     * Create a test case from a prompt
     */
    public TestCase createTestCaseFromPrompt(String prompt, String url) {
        TestCase testCase = new TestCase();
        
        // Generate test name from prompt
        String testName = generateTestName(prompt);
        testCase.setName(testName);
        testCase.setDescription(prompt);
        testCase.setUrl(url);
        testCase.addTag("generated");
        testCase.setCreatedBy("AI Generator");
        
        // Process prompt to get steps
        List<TestStep> steps = processPrompt(prompt);
        testCase.setSteps(steps);
        
        // Set estimated duration based on number of steps
        int estimatedDuration = steps.size() * 10; // 10 seconds per step
        testCase.setEstimatedDuration(estimatedDuration);
        
        return testCase;
    }
    
    /**
     * Generate a test name from prompt
     */
    private String generateTestName(String prompt) {
        // Take first few words and clean them up
        String[] words = prompt.split("\\s+");
        StringBuilder name = new StringBuilder("test");
        
        int wordCount = 0;
        for (String word : words) {
            if (wordCount >= 5) break; // Limit to 5 words
            
            String cleanWord = word.replaceAll("[^a-zA-Z0-9]", "");
            if (!cleanWord.isEmpty() && cleanWord.length() > 2) {
                name.append(Character.toUpperCase(cleanWord.charAt(0)))
                    .append(cleanWord.substring(1).toLowerCase());
                wordCount++;
            }
        }
        
        return name.toString();
    }
}
