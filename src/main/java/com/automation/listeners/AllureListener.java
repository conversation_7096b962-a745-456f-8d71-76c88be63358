package com.automation.listeners;

import com.automation.utils.ScreenshotUtils;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.model.Status;
import io.qameta.allure.model.StatusDetails;
import io.qameta.allure.testng.AllureTestNg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.ITestResult;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * Allure TestNG Listener for enhanced reporting
 * Extends AllureTestNg to add custom functionality
 */
public class AllureListener extends AllureTestNg {
    
    private static final Logger logger = LoggerFactory.getLogger(AllureListener.class);
    
    @Override
    public void onTestStart(ITestResult result) {
        super.onTestStart(result);
        
        String testName = result.getMethod().getMethodName();
        String className = result.getTestClass().getName();
        
        // Add test information to Allure
        Allure.epic("Playwright Automation Framework");
        Allure.feature(getFeatureName(className));
        Allure.story(testName);
        
        // Add test description if available
        String description = result.getMethod().getDescription();
        if (description != null && !description.trim().isEmpty()) {
            Allure.description(description);
        }
        
        // Add test groups as labels
        String[] groups = result.getMethod().getGroups();
        for (String group : groups) {
            Allure.label("group", group);
        }
        
        // Add environment information
        addEnvironmentInformation();
        
        logger.debug("Allure test started: {}", testName);
    }
    
    @Override
    public void onTestSuccess(ITestResult result) {
        String testName = result.getMethod().getMethodName();
        
        // Add success screenshot if enabled
        String screenshotPath = ScreenshotUtils.takeSuccessScreenshot(testName);
        if (screenshotPath != null) {
            attachScreenshot(screenshotPath, "Test Success Screenshot");
        }
        
        // Add step for test completion
        Allure.step("Test completed successfully", () -> {
            Allure.addAttachment("Test Status", "text/plain", "PASSED");
        });
        
        super.onTestSuccess(result);
        logger.debug("Allure test success: {}", testName);
    }
    
    @Override
    public void onTestFailure(ITestResult result) {
        String testName = result.getMethod().getMethodName();
        Throwable throwable = result.getThrowable();
        
        // Add failure screenshot
        String screenshotPath = ScreenshotUtils.takeFailureScreenshot(testName);
        if (screenshotPath != null) {
            attachScreenshot(screenshotPath, "Test Failure Screenshot");
        }
        
        // Add failure details
        if (throwable != null) {
            Allure.addAttachment("Error Message", "text/plain", throwable.getMessage());
            Allure.addAttachment("Stack Trace", "text/plain", getStackTrace(throwable));
        }
        
        // Add step for test failure
        Allure.step("Test failed", () -> {
            Allure.addAttachment("Test Status", "text/plain", "FAILED");
            if (throwable != null) {
                Allure.addAttachment("Failure Reason", "text/plain", throwable.getMessage());
            }
        });
        
        super.onTestFailure(result);
        logger.debug("Allure test failure: {}", testName);
    }
    
    @Override
    public void onTestSkipped(ITestResult result) {
        String testName = result.getMethod().getMethodName();
        Throwable throwable = result.getThrowable();
        
        // Add skip reason if available
        if (throwable != null) {
            Allure.addAttachment("Skip Reason", "text/plain", throwable.getMessage());
        }
        
        // Add step for test skip
        Allure.step("Test skipped", () -> {
            Allure.addAttachment("Test Status", "text/plain", "SKIPPED");
            if (throwable != null) {
                Allure.addAttachment("Skip Reason", "text/plain", throwable.getMessage());
            }
        });
        
        super.onTestSkipped(result);
        logger.debug("Allure test skipped: {}", testName);
    }
    
    /**
     * Attach screenshot to Allure report
     */
    public static void attachScreenshot(String screenshotPath, String name) {
        if (screenshotPath != null && Files.exists(Paths.get(screenshotPath))) {
            try {
                byte[] screenshot = Files.readAllBytes(Paths.get(screenshotPath));
                Allure.addAttachment(name, "image/png", new ByteArrayInputStream(screenshot), ".png");
                logger.debug("Screenshot attached to Allure: {}", screenshotPath);
            } catch (IOException e) {
                logger.warn("Failed to attach screenshot to Allure: {}", screenshotPath, e);
            }
        }
    }
    
    /**
     * Add step to Allure report
     */
    public static void addStep(String stepName) {
        Allure.step(stepName);
    }
    
    /**
     * Add step with description to Allure report
     */
    public static void addStep(String stepName, String description) {
        Allure.step(stepName, () -> {
            Allure.addAttachment("Step Description", "text/plain", description);
        });
    }
    
    /**
     * Add parameter to Allure report
     */
    public static void addParameter(String name, String value) {
        Allure.parameter(name, value);
    }
    
    /**
     * Add link to Allure report
     */
    public static void addLink(String name, String url) {
        Allure.link(name, url);
    }
    
    /**
     * Add issue link to Allure report
     */
    public static void addIssue(String issueId) {
        Allure.issue(issueId);
    }
    
    /**
     * Add test case link to Allure report
     */
    public static void addTestCase(String testCaseId) {
        Allure.tms(testCaseId);
    }
    
    /**
     * Add severity to Allure report
     */
    public static void addSeverity(io.qameta.allure.SeverityLevel severity) {
        Allure.severity(severity);
    }
    
    /**
     * Add environment information
     */
    private void addEnvironmentInformation() {
        try {
            // Add browser information
            String browser = System.getProperty("automation.browser", "chromium");
            Allure.parameter("Browser", browser);
            
            // Add headless mode
            String headless = System.getProperty("automation.headless", "false");
            Allure.parameter("Headless Mode", headless);
            
            // Add environment
            String environment = System.getProperty("environment", "dev");
            Allure.parameter("Environment", environment);
            
            // Add base URL
            String baseUrl = System.getProperty("automation.base.url", "");
            if (!baseUrl.isEmpty()) {
                Allure.parameter("Base URL", baseUrl);
            }
            
            // Add OS information
            Allure.parameter("Operating System", System.getProperty("os.name"));
            Allure.parameter("Java Version", System.getProperty("java.version"));
            
        } catch (Exception e) {
            logger.warn("Failed to add environment information to Allure: {}", e.getMessage());
        }
    }
    
    /**
     * Get feature name from class name
     */
    private String getFeatureName(String className) {
        if (className == null) {
            return "Unknown Feature";
        }
        
        // Extract simple class name
        String simpleName = className.substring(className.lastIndexOf('.') + 1);
        
        // Convert camelCase to readable format
        return simpleName.replaceAll("([A-Z])", " $1").trim();
    }
    
    /**
     * Get stack trace as string
     */
    private String getStackTrace(Throwable throwable) {
        if (throwable == null) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(throwable.toString()).append("\n");
        
        for (StackTraceElement element : throwable.getStackTrace()) {
            sb.append("\tat ").append(element.toString()).append("\n");
        }
        
        Throwable cause = throwable.getCause();
        if (cause != null) {
            sb.append("Caused by: ").append(getStackTrace(cause));
        }
        
        return sb.toString();
    }
    
    /**
     * Add custom attachment to Allure
     */
    public static void addAttachment(String name, String content) {
        Allure.addAttachment(name, "text/plain", content);
    }
    
    /**
     * Add JSON attachment to Allure
     */
    public static void addJsonAttachment(String name, String jsonContent) {
        Allure.addAttachment(name, "application/json", jsonContent);
    }
    
    /**
     * Add HTML attachment to Allure
     */
    public static void addHtmlAttachment(String name, String htmlContent) {
        Allure.addAttachment(name, "text/html", htmlContent);
    }
}
