package com.automation.listeners;

import com.automation.reporting.ExtentManager;
import com.automation.utils.ScreenshotUtils;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.Status;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.*;

/**
 * ExtentReports TestNG Listener for detailed test reporting
 * Integrates with ExtentManager for comprehensive test reporting
 */
public class ExtentReportListener implements ITestListener, ISuiteListener {
    
    private static final Logger logger = LoggerFactory.getLogger(ExtentReportListener.class);
    
    @Override
    public void onStart(ISuite suite) {
        logger.info("Initializing ExtentReports for suite: {}", suite.getName());
        ExtentManager.initializeExtentReports();
    }
    
    @Override
    public void onFinish(ISuite suite) {
        logger.info("Finalizing ExtentReports for suite: {}", suite.getName());
        ExtentManager.flushReports();
    }
    
    @Override
    public void onTestStart(ITestResult result) {
        String testName = result.getMethod().getMethodName();
        String description = getTestDescription(result);
        String className = result.getTestClass().getName();
        
        // Create test in ExtentReports
        ExtentTest test = ExtentManager.createTest(testName, description);
        
        // Add test information
        test.assignCategory(getTestCategories(result));
        test.assignAuthor(getTestAuthor(className));
        test.assignDevice(getTestDevice());
        
        // Log test start
        ExtentManager.logInfo("Test started: " + testName);
        ExtentManager.logInfo("Test class: " + className);
        ExtentManager.logInfo("Test description: " + description);
        
        // Add test groups
        String[] groups = result.getMethod().getGroups();
        if (groups.length > 0) {
            ExtentManager.logInfo("Test groups: " + String.join(", ", groups));
        }
        
        // Add environment information
        addEnvironmentInfo();
        
        logger.debug("ExtentReports test started: {}", testName);
    }
    
    @Override
    public void onTestSuccess(ITestResult result) {
        String testName = result.getMethod().getMethodName();
        
        ExtentManager.logPass("Test completed successfully");
        
        // Add success screenshot if enabled
        String screenshotPath = ScreenshotUtils.takeSuccessScreenshot(testName);
        if (screenshotPath != null) {
            ExtentManager.addScreenshot(screenshotPath, Status.PASS, "Test Success Screenshot");
        }
        
        // Add test duration
        long duration = result.getEndMillis() - result.getStartMillis();
        ExtentManager.logInfo("Test duration: " + duration + " ms (" + (duration / 1000.0) + " seconds)");
        
        logger.debug("ExtentReports test success: {}", testName);
    }
    
    @Override
    public void onTestFailure(ITestResult result) {
        String testName = result.getMethod().getMethodName();
        Throwable throwable = result.getThrowable();
        
        // Log failure
        String errorMessage = throwable != null ? throwable.getMessage() : "Unknown error";
        ExtentManager.logFail("Test failed: " + errorMessage);
        
        if (throwable != null) {
            ExtentManager.logFail("Stack trace", throwable);
        }
        
        // Add failure screenshot
        String screenshotPath = ScreenshotUtils.takeFailureScreenshot(testName);
        if (screenshotPath != null) {
            ExtentManager.addScreenshot(screenshotPath, Status.FAIL, "Test Failure Screenshot");
        }
        
        // Add test duration
        long duration = result.getEndMillis() - result.getStartMillis();
        ExtentManager.logInfo("Test duration: " + duration + " ms (" + (duration / 1000.0) + " seconds)");
        
        logger.debug("ExtentReports test failure: {}", testName);
    }
    
    @Override
    public void onTestSkipped(ITestResult result) {
        String testName = result.getMethod().getMethodName();
        Throwable throwable = result.getThrowable();
        
        // Log skip
        String skipReason = throwable != null ? throwable.getMessage() : "Unknown reason";
        ExtentManager.logSkip("Test skipped: " + skipReason);
        
        if (throwable != null) {
            ExtentManager.logSkip("Skip details: " + throwable.getMessage());
        }
        
        logger.debug("ExtentReports test skipped: {}", testName);
    }
    
    @Override
    public void onTestFailedButWithinSuccessPercentage(ITestResult result) {
        String testName = result.getMethod().getMethodName();
        ExtentManager.logWarning("Test failed but within success percentage: " + testName);
        
        logger.debug("ExtentReports test failed but within success percentage: {}", testName);
    }
    
    /**
     * Get test description
     */
    private String getTestDescription(ITestResult result) {
        String description = result.getMethod().getDescription();
        return description != null && !description.trim().isEmpty() ? description : "No description provided";
    }
    
    /**
     * Get test categories based on groups and class name
     */
    private String[] getTestCategories(ITestResult result) {
        String[] groups = result.getMethod().getGroups();
        String className = result.getTestClass().getName();
        
        if (groups.length > 0) {
            return groups;
        } else {
            // Extract category from class name
            String simpleName = className.substring(className.lastIndexOf('.') + 1);
            if (simpleName.toLowerCase().contains("smoke")) {
                return new String[]{"Smoke"};
            } else if (simpleName.toLowerCase().contains("regression")) {
                return new String[]{"Regression"};
            } else if (simpleName.toLowerCase().contains("api")) {
                return new String[]{"API"};
            } else if (simpleName.toLowerCase().contains("generated")) {
                return new String[]{"Generated"};
            } else {
                return new String[]{"Functional"};
            }
        }
    }
    
    /**
     * Get test author from class name
     */
    private String getTestAuthor(String className) {
        // Extract package name to determine author/team
        if (className.contains("smoke")) {
            return "QA Team";
        } else if (className.contains("api")) {
            return "API Team";
        } else if (className.contains("generated")) {
            return "AI Generator";
        } else {
            return "Automation Team";
        }
    }
    
    /**
     * Get test device/browser information
     */
    private String getTestDevice() {
        String browser = System.getProperty("automation.browser", "chromium");
        String headless = System.getProperty("automation.headless", "false");
        return browser + (Boolean.parseBoolean(headless) ? " (Headless)" : " (Headed)");
    }
    
    /**
     * Add environment information to the report
     */
    private void addEnvironmentInfo() {
        try {
            // Browser information
            String browser = System.getProperty("automation.browser", "chromium");
            ExtentManager.logInfo("Browser: " + browser);
            
            // Headless mode
            String headless = System.getProperty("automation.headless", "false");
            ExtentManager.logInfo("Headless mode: " + headless);
            
            // Environment
            String environment = System.getProperty("environment", "dev");
            ExtentManager.logInfo("Environment: " + environment);
            
            // Base URL
            String baseUrl = System.getProperty("automation.base.url", "");
            if (!baseUrl.isEmpty()) {
                ExtentManager.logInfo("Base URL: " + baseUrl);
            }
            
            // OS information
            ExtentManager.logInfo("Operating System: " + System.getProperty("os.name"));
            ExtentManager.logInfo("Java Version: " + System.getProperty("java.version"));
            
        } catch (Exception e) {
            logger.warn("Failed to add environment information to ExtentReports: {}", e.getMessage());
        }
    }
    
    /**
     * Log custom step in ExtentReports
     */
    public static void logStep(String stepDescription) {
        ExtentManager.logInfo("Step: " + stepDescription);
    }
    
    /**
     * Log custom step with status
     */
    public static void logStep(String stepDescription, Status status) {
        switch (status) {
            case PASS:
                ExtentManager.logPass("Step: " + stepDescription);
                break;
            case FAIL:
                ExtentManager.logFail("Step: " + stepDescription);
                break;
            case WARNING:
                ExtentManager.logWarning("Step: " + stepDescription);
                break;
            case SKIP:
                ExtentManager.logSkip("Step: " + stepDescription);
                break;
            default:
                ExtentManager.logInfo("Step: " + stepDescription);
                break;
        }
    }
    
    /**
     * Add screenshot with custom message
     */
    public static void addScreenshotWithMessage(String message) {
        String screenshotPath = ScreenshotUtils.takeScreenshot(message);
        if (screenshotPath != null) {
            ExtentManager.addScreenshot(screenshotPath, message);
        }
    }
    
    /**
     * Add custom attachment to report
     */
    public static void addAttachment(String name, String content) {
        ExtentManager.logInfo(name + ": " + content);
    }
}
