package com.automation.listeners;

import com.automation.utils.ScreenshotUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * TestNG Listener for handling test lifecycle events
 * Provides logging and basic test management functionality
 */
public class TestListener implements ITestListener, ISuiteListener, IInvokedMethodListener {
    
    private static final Logger logger = LoggerFactory.getLogger(TestListener.class);
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private long suiteStartTime;
    private long testStartTime;
    
    // Suite level events
    
    @Override
    public void onStart(ISuite suite) {
        suiteStartTime = System.currentTimeMillis();
        logger.info("=".repeat(80));
        logger.info("TEST SUITE STARTED: {}", suite.getName());
        logger.info("Suite Start Time: {}", LocalDateTime.now().format(TIMESTAMP_FORMAT));
        logger.info("=".repeat(80));
        
        // Clean up old screenshots at suite start
        ScreenshotUtils.cleanupOldScreenshots();
    }
    
    @Override
    public void onFinish(ISuite suite) {
        long suiteEndTime = System.currentTimeMillis();
        long suiteDuration = suiteEndTime - suiteStartTime;
        
        logger.info("=".repeat(80));
        logger.info("TEST SUITE FINISHED: {}", suite.getName());
        logger.info("Suite End Time: {}", LocalDateTime.now().format(TIMESTAMP_FORMAT));
        logger.info("Suite Duration: {} ms ({} seconds)", suiteDuration, suiteDuration / 1000.0);
        logger.info("=".repeat(80));
        
        // Print suite summary
        printSuiteSummary(suite);
    }
    
    // Test level events
    
    @Override
    public void onTestStart(ITestResult result) {
        testStartTime = System.currentTimeMillis();
        String testName = getTestName(result);
        String className = result.getTestClass().getName();
        
        logger.info("-".repeat(60));
        logger.info("TEST STARTED: {}", testName);
        logger.info("Test Class: {}", className);
        logger.info("Test Start Time: {}", LocalDateTime.now().format(TIMESTAMP_FORMAT));
        logger.info("Test Description: {}", getTestDescription(result));
        logger.info("Test Groups: {}", String.join(", ", result.getMethod().getGroups()));
        logger.info("-".repeat(60));
    }
    
    @Override
    public void onTestSuccess(ITestResult result) {
        long testEndTime = System.currentTimeMillis();
        long testDuration = testEndTime - testStartTime;
        String testName = getTestName(result);
        
        logger.info("✅ TEST PASSED: {}", testName);
        logger.info("Test Duration: {} ms ({} seconds)", testDuration, testDuration / 1000.0);
        logger.info("Test End Time: {}", LocalDateTime.now().format(TIMESTAMP_FORMAT));
        
        // Take success screenshot if enabled
        ScreenshotUtils.takeSuccessScreenshot(testName);
    }
    
    @Override
    public void onTestFailure(ITestResult result) {
        long testEndTime = System.currentTimeMillis();
        long testDuration = testEndTime - testStartTime;
        String testName = getTestName(result);
        Throwable throwable = result.getThrowable();
        
        logger.error("❌ TEST FAILED: {}", testName);
        logger.error("Test Duration: {} ms ({} seconds)", testDuration, testDuration / 1000.0);
        logger.error("Test End Time: {}", LocalDateTime.now().format(TIMESTAMP_FORMAT));
        logger.error("Failure Reason: {}", throwable != null ? throwable.getMessage() : "Unknown");
        
        if (throwable != null) {
            logger.error("Stack Trace:", throwable);
        }
        
        // Take failure screenshot
        String screenshotPath = ScreenshotUtils.takeFailureScreenshot(testName);
        if (screenshotPath != null) {
            logger.info("Failure screenshot saved: {}", screenshotPath);
        }
    }
    
    @Override
    public void onTestSkipped(ITestResult result) {
        String testName = getTestName(result);
        Throwable throwable = result.getThrowable();
        
        logger.warn("⏭️ TEST SKIPPED: {}", testName);
        logger.warn("Skip Reason: {}", throwable != null ? throwable.getMessage() : "Unknown");
        logger.warn("Test End Time: {}", LocalDateTime.now().format(TIMESTAMP_FORMAT));
    }
    
    @Override
    public void onTestFailedButWithinSuccessPercentage(ITestResult result) {
        String testName = getTestName(result);
        logger.warn("⚠️ TEST FAILED BUT WITHIN SUCCESS PERCENTAGE: {}", testName);
    }
    
    // Configuration method events
    
    @Override
    public void beforeInvocation(IInvokedMethod method, ITestResult testResult) {
        if (method.isConfigurationMethod()) {
            String methodName = method.getTestMethod().getMethodName();
            String methodType = getConfigurationMethodType(method);
            logger.debug("Configuration method started: {} ({})", methodName, methodType);
        }
    }
    
    @Override
    public void afterInvocation(IInvokedMethod method, ITestResult testResult) {
        if (method.isConfigurationMethod()) {
            String methodName = method.getTestMethod().getMethodName();
            String methodType = getConfigurationMethodType(method);
            
            if (testResult.getStatus() == ITestResult.FAILURE) {
                logger.error("Configuration method failed: {} ({})", methodName, methodType);
                if (testResult.getThrowable() != null) {
                    logger.error("Configuration failure reason:", testResult.getThrowable());
                }
            } else {
                logger.debug("Configuration method completed: {} ({})", methodName, methodType);
            }
        }
    }
    
    // Utility methods
    
    private String getTestName(ITestResult result) {
        return result.getMethod().getMethodName();
    }
    
    private String getTestDescription(ITestResult result) {
        String description = result.getMethod().getDescription();
        return description != null && !description.trim().isEmpty() ? description : "No description provided";
    }
    
    private String getConfigurationMethodType(IInvokedMethod method) {
        if (method.getTestMethod().isBeforeSuiteConfiguration()) return "BeforeSuite";
        if (method.getTestMethod().isAfterSuiteConfiguration()) return "AfterSuite";
        if (method.getTestMethod().isBeforeTestConfiguration()) return "BeforeTest";
        if (method.getTestMethod().isAfterTestConfiguration()) return "AfterTest";
        if (method.getTestMethod().isBeforeClassConfiguration()) return "BeforeClass";
        if (method.getTestMethod().isAfterClassConfiguration()) return "AfterClass";
        if (method.getTestMethod().isBeforeMethodConfiguration()) return "BeforeMethod";
        if (method.getTestMethod().isAfterMethodConfiguration()) return "AfterMethod";
        if (method.getTestMethod().isBeforeGroupsConfiguration()) return "BeforeGroups";
        if (method.getTestMethod().isAfterGroupsConfiguration()) return "AfterGroups";
        return "Unknown";
    }
    
    private void printSuiteSummary(ISuite suite) {
        int totalTests = 0;
        int passedTests = 0;
        int failedTests = 0;
        int skippedTests = 0;
        
        for (ISuiteResult suiteResult : suite.getResults().values()) {
            ITestContext testContext = suiteResult.getTestContext();
            totalTests += testContext.getAllTestMethods().length;
            passedTests += testContext.getPassedTests().size();
            failedTests += testContext.getFailedTests().size();
            skippedTests += testContext.getSkippedTests().size();
        }
        
        logger.info("SUITE SUMMARY:");
        logger.info("Total Tests: {}", totalTests);
        logger.info("Passed: {} ({}%)", passedTests, 
                   totalTests > 0 ? String.format("%.1f", (passedTests * 100.0) / totalTests) : "0");
        logger.info("Failed: {} ({}%)", failedTests, 
                   totalTests > 0 ? String.format("%.1f", (failedTests * 100.0) / totalTests) : "0");
        logger.info("Skipped: {} ({}%)", skippedTests, 
                   totalTests > 0 ? String.format("%.1f", (skippedTests * 100.0) / totalTests) : "0");
        
        if (failedTests > 0) {
            logger.info("FAILED TESTS:");
            for (ISuiteResult suiteResult : suite.getResults().values()) {
                ITestContext testContext = suiteResult.getTestContext();
                testContext.getFailedTests().getAllResults().forEach(result -> {
                    logger.info("  - {} ({})", result.getMethod().getMethodName(), 
                               result.getThrowable() != null ? result.getThrowable().getMessage() : "Unknown reason");
                });
            }
        }
    }
}
