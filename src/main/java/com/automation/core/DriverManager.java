package com.automation.core;

import com.microsoft.playwright.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Paths;
import java.util.Arrays;

/**
 * Driver Manager for Playwright Browser Management
 * Handles browser initialization, configuration, and cleanup
 */
public class DriverManager {
    
    private static final Logger logger = LoggerFactory.getLogger(DriverManager.class);
    private static final ThreadLocal<Playwright> playwright = new ThreadLocal<>();
    private static final ThreadLocal<Browser> browser = new ThreadLocal<>();
    private static final ThreadLocal<BrowserContext> context = new ThreadLocal<>();
    private static final ThreadLocal<Page> page = new ThreadLocal<>();
    
    /**
     * Initialize Playwright and Browser
     */
    public static void initializeDriver() {
        try {
            // Clean up any existing instances first
            quitDriver();

            // Create Playwright instance
            Playwright playwrightInstance = Playwright.create();
            playwright.set(playwrightInstance);

            // Get browser configuration
            String browserName = System.getProperty("automation.browser", "chromium");
            boolean headless = Boolean.parseBoolean(System.getProperty("automation.headless", "false"));

            logger.info("Initializing browser: {} (headless: {})", browserName, headless);

            // Launch browser
            Browser browserInstance = launchBrowser(playwrightInstance, browserName, headless);
            browser.set(browserInstance);

            // Create browser context
            BrowserContext contextInstance = createBrowserContext(browserInstance);
            context.set(contextInstance);

            // Create new page
            Page pageInstance = contextInstance.newPage();
            page.set(pageInstance);

            logger.info("Driver initialized successfully with browser: {}", browserName);

        } catch (Exception e) {
            logger.error("Failed to initialize driver: {}", e.getMessage(), e);
            // Clean up on failure
            quitDriver();
            throw new RuntimeException("Driver initialization failed", e);
        }
    }
    
    /**
     * Launch browser based on configuration
     */
    private static Browser launchBrowser(Playwright playwright, String browserName, boolean headless) {
        List<String> args = new ArrayList<>(Arrays.asList(
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--no-sandbox",
            "--disable-dev-shm-usage"
        ));

        // Add additional args for headless mode
        if (headless) {
            args.addAll(Arrays.asList(
                "--disable-gpu",
                "--disable-extensions",
                "--no-first-run",
                "--disable-default-apps"
            ));
        } else {
            // For headed mode, add args to prevent browser from closing
            args.addAll(Arrays.asList(
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding"
            ));
        }

        BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
                .setHeadless(headless)
                .setSlowMo(headless ? 50 : 200) // Slower for headed mode to see actions
                .setTimeout(60000)
                .setArgs(args);

        switch (browserName.toLowerCase()) {
            case "firefox":
                return playwright.firefox().launch(launchOptions);
            case "webkit":
            case "safari":
                return playwright.webkit().launch(launchOptions);
            case "chromium":
            case "chrome":
            default:
                return playwright.chromium().launch(launchOptions);
        }
    }
    
    /**
     * Create browser context with configurations
     */
    private static BrowserContext createBrowserContext(Browser browser) {
        Browser.NewContextOptions contextOptions = new Browser.NewContextOptions()
                .setViewportSize(1920, 1080)
                .setLocale("en-US")
                .setTimezoneId("America/New_York");
        
        return browser.newContext(contextOptions);
    }
    
    /**
     * Get current Page instance
     */
    public static Page getPage() {
        Page currentPage = page.get();
        if (currentPage == null || currentPage.isClosed()) {
            logger.warn("Page is null or closed, reinitializing driver");
            initializeDriver();
            currentPage = page.get();
        }
        return currentPage;
    }

    /**
     * Check if driver is properly initialized
     */
    public static boolean isDriverInitialized() {
        try {
            Page currentPage = page.get();
            Browser currentBrowser = browser.get();
            BrowserContext currentContext = context.get();
            Playwright currentPlaywright = playwright.get();

            return currentPage != null && !currentPage.isClosed() &&
                   currentBrowser != null && currentBrowser.isConnected() &&
                   currentContext != null &&
                   currentPlaywright != null;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Navigate to URL
     */
    public static void navigateTo(String url) {
        Page currentPage = getPage();
        if (currentPage != null) {
            currentPage.navigate(url);
            logger.info("Navigated to: {}", url);
        } else {
            throw new RuntimeException("Page not initialized");
        }
    }
    
    /**
     * Take screenshot
     */
    public static byte[] takeScreenshot() {
        Page currentPage = getPage();
        if (currentPage != null) {
            return currentPage.screenshot();
        }
        return null;
    }
    
    /**
     * Quit driver and cleanup resources
     */
    public static void quitDriver() {
        try {
            // Close page
            Page currentPage = page.get();
            if (currentPage != null && !currentPage.isClosed()) {
                try {
                    currentPage.close();
                } catch (Exception e) {
                    logger.warn("Error closing page: {}", e.getMessage());
                }
                page.remove();
            }

            // Close context
            BrowserContext currentContext = context.get();
            if (currentContext != null) {
                try {
                    currentContext.close();
                } catch (Exception e) {
                    logger.warn("Error closing context: {}", e.getMessage());
                }
                context.remove();
            }

            // Close browser
            Browser currentBrowser = browser.get();
            if (currentBrowser != null && currentBrowser.isConnected()) {
                try {
                    currentBrowser.close();
                } catch (Exception e) {
                    logger.warn("Error closing browser: {}", e.getMessage());
                }
                browser.remove();
            }

            // Close playwright
            Playwright currentPlaywright = playwright.get();
            if (currentPlaywright != null) {
                try {
                    currentPlaywright.close();
                } catch (Exception e) {
                    logger.warn("Error closing playwright: {}", e.getMessage());
                }
                playwright.remove();
            }

            logger.debug("Driver cleanup completed");

        } catch (Exception e) {
            logger.error("Error during driver cleanup: {}", e.getMessage());
        }
    }
    
    /**
     * Get browser information
     */
    public static String getBrowserInfo() {
        Browser currentBrowser = browser.get();
        if (currentBrowser != null) {
            return currentBrowser.browserType().name() + " " + currentBrowser.version();
        }
        return "Browser not initialized";
    }
}
