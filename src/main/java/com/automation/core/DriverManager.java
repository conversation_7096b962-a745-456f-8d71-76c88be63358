package com.automation.core;

import com.microsoft.playwright.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Paths;
import java.util.Arrays;

/**
 * Driver Manager for Playwright Browser Management
 * Handles browser initialization, configuration, and cleanup
 */
public class DriverManager {
    
    private static final Logger logger = LoggerFactory.getLogger(DriverManager.class);
    private static final ThreadLocal<Playwright> playwright = new ThreadLocal<>();
    private static final ThreadLocal<Browser> browser = new ThreadLocal<>();
    private static final ThreadLocal<BrowserContext> context = new ThreadLocal<>();
    private static final ThreadLocal<Page> page = new ThreadLocal<>();
    
    /**
     * Initialize Playwright and Browser
     */
    public static void initializeDriver() {
        try {
            // Clean up any existing instances first
            quitDriver();

            // Create Playwright instance
            Playwright playwrightInstance = Playwright.create();
            playwright.set(playwrightInstance);

            // Get browser configuration
            String browserName = System.getProperty("automation.browser", "chromium");
            boolean headless = Boolean.parseBoolean(System.getProperty("automation.headless", "false"));

            logger.info("Initializing browser: {} (headless: {})", browserName, headless);

            // Launch browser
            Browser browserInstance = launchBrowser(playwrightInstance, browserName, headless);
            browser.set(browserInstance);

            // Create browser context
            BrowserContext contextInstance = createBrowserContext(browserInstance);
            context.set(contextInstance);

            // Create new page
            Page pageInstance = contextInstance.newPage();
            page.set(pageInstance);

            logger.info("Driver initialized successfully with browser: {}", browserName);

        } catch (Exception e) {
            logger.error("Failed to initialize driver: {}", e.getMessage(), e);
            // Clean up on failure
            quitDriver();
            throw new RuntimeException("Driver initialization failed", e);
        }
    }
    
    /**
     * Launch browser based on configuration
     */
    private static Browser launchBrowser(Playwright playwright, String browserName, boolean headless) {
        BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
                .setHeadless(headless)
                .setSlowMo(50)
                .setArgs(Arrays.asList(
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--no-sandbox"
                ));
        
        switch (browserName.toLowerCase()) {
            case "firefox":
                return playwright.firefox().launch(launchOptions);
            case "webkit":
            case "safari":
                return playwright.webkit().launch(launchOptions);
            case "chromium":
            case "chrome":
            default:
                return playwright.chromium().launch(launchOptions);
        }
    }
    
    /**
     * Create browser context with configurations
     */
    private static BrowserContext createBrowserContext(Browser browser) {
        Browser.NewContextOptions contextOptions = new Browser.NewContextOptions()
                .setViewportSize(1920, 1080)
                .setLocale("en-US")
                .setTimezoneId("America/New_York");
        
        return browser.newContext(contextOptions);
    }
    
    /**
     * Get current Page instance
     */
    public static Page getPage() {
        return page.get();
    }
    
    /**
     * Navigate to URL
     */
    public static void navigateTo(String url) {
        Page currentPage = getPage();
        if (currentPage != null) {
            currentPage.navigate(url);
            logger.info("Navigated to: {}", url);
        } else {
            throw new RuntimeException("Page not initialized");
        }
    }
    
    /**
     * Take screenshot
     */
    public static byte[] takeScreenshot() {
        Page currentPage = getPage();
        if (currentPage != null) {
            return currentPage.screenshot();
        }
        return null;
    }
    
    /**
     * Quit driver and cleanup resources
     */
    public static void quitDriver() {
        try {
            Page currentPage = page.get();
            if (currentPage != null) {
                currentPage.close();
                page.remove();
            }
            
            BrowserContext currentContext = context.get();
            if (currentContext != null) {
                currentContext.close();
                context.remove();
            }
            
            Browser currentBrowser = browser.get();
            if (currentBrowser != null) {
                currentBrowser.close();
                browser.remove();
            }
            
            Playwright currentPlaywright = playwright.get();
            if (currentPlaywright != null) {
                currentPlaywright.close();
                playwright.remove();
            }
            
            logger.info("Driver cleanup completed successfully");
            
        } catch (Exception e) {
            logger.error("Error during driver cleanup: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Get browser information
     */
    public static String getBrowserInfo() {
        Browser currentBrowser = browser.get();
        if (currentBrowser != null) {
            return currentBrowser.browserType().name() + " " + currentBrowser.version();
        }
        return "Browser not initialized";
    }
}
