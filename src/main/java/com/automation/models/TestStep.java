package com.automation.models;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;

/**
 * Test Step Model for representing individual test steps
 * Used within test cases for detailed test execution
 */
public class TestStep {
    
    private String id;
    private int stepNumber;
    private String action;
    private String description;
    private String locator;
    private String inputData;
    private String expectedResult;
    private String actualResult;
    private String status;
    private String screenshotPath;
    private LocalDateTime executedAt;
    private long executionTime; // in milliseconds
    private String errorMessage;
    private Map<String, String> additionalData;
    
    // Action types
    public static final String ACTION_NAVIGATE = "NAVIGATE";
    public static final String ACTION_CLICK = "CLICK";
    public static final String ACTION_TYPE = "TYPE";
    public static final String ACTION_SELECT = "SELECT";
    public static final String ACTION_VERIFY = "VERIFY";
    public static final String ACTION_WAIT = "WAIT";
    public static final String ACTION_SCROLL = "SCROLL";
    public static final String ACTION_HOVER = "HOVER";
    public static final String ACTION_DRAG_DROP = "DRAG_DROP";
    public static final String ACTION_UPLOAD = "UPLOAD";
    public static final String ACTION_DOWNLOAD = "DOWNLOAD";
    public static final String ACTION_SWITCH_FRAME = "SWITCH_FRAME";
    public static final String ACTION_SWITCH_WINDOW = "SWITCH_WINDOW";
    public static final String ACTION_EXECUTE_JS = "EXECUTE_JS";
    public static final String ACTION_API_CALL = "API_CALL";
    
    // Status types
    public static final String STATUS_PENDING = "PENDING";
    public static final String STATUS_RUNNING = "RUNNING";
    public static final String STATUS_PASSED = "PASSED";
    public static final String STATUS_FAILED = "FAILED";
    public static final String STATUS_SKIPPED = "SKIPPED";
    
    public TestStep() {
        this.additionalData = new HashMap<>();
        this.status = STATUS_PENDING;
    }
    
    public TestStep(int stepNumber, String action, String description) {
        this();
        this.stepNumber = stepNumber;
        this.action = action;
        this.description = description;
    }
    
    public TestStep(int stepNumber, String action, String description, String locator) {
        this(stepNumber, action, description);
        this.locator = locator;
    }
    
    // Getters and Setters
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public int getStepNumber() {
        return stepNumber;
    }
    
    public void setStepNumber(int stepNumber) {
        this.stepNumber = stepNumber;
    }
    
    public String getAction() {
        return action;
    }
    
    public void setAction(String action) {
        this.action = action;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getLocator() {
        return locator;
    }
    
    public void setLocator(String locator) {
        this.locator = locator;
    }
    
    public String getInputData() {
        return inputData;
    }
    
    public void setInputData(String inputData) {
        this.inputData = inputData;
    }
    
    public String getExpectedResult() {
        return expectedResult;
    }
    
    public void setExpectedResult(String expectedResult) {
        this.expectedResult = expectedResult;
    }
    
    public String getActualResult() {
        return actualResult;
    }
    
    public void setActualResult(String actualResult) {
        this.actualResult = actualResult;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getScreenshotPath() {
        return screenshotPath;
    }
    
    public void setScreenshotPath(String screenshotPath) {
        this.screenshotPath = screenshotPath;
    }
    
    public LocalDateTime getExecutedAt() {
        return executedAt;
    }
    
    public void setExecutedAt(LocalDateTime executedAt) {
        this.executedAt = executedAt;
    }
    
    public long getExecutionTime() {
        return executionTime;
    }
    
    public void setExecutionTime(long executionTime) {
        this.executionTime = executionTime;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public Map<String, String> getAdditionalData() {
        return additionalData;
    }
    
    public void setAdditionalData(Map<String, String> additionalData) {
        this.additionalData = additionalData;
    }
    
    public void addAdditionalData(String key, String value) {
        this.additionalData.put(key, value);
    }
    
    // Utility methods
    
    public boolean isPassed() {
        return STATUS_PASSED.equals(status);
    }
    
    public boolean isFailed() {
        return STATUS_FAILED.equals(status);
    }
    
    public boolean isSkipped() {
        return STATUS_SKIPPED.equals(status);
    }
    
    public boolean isPending() {
        return STATUS_PENDING.equals(status);
    }
    
    public boolean isRunning() {
        return STATUS_RUNNING.equals(status);
    }
    
    public void markAsStarted() {
        this.status = STATUS_RUNNING;
        this.executedAt = LocalDateTime.now();
    }
    
    public void markAsPassed() {
        this.status = STATUS_PASSED;
    }
    
    public void markAsFailed(String errorMessage) {
        this.status = STATUS_FAILED;
        this.errorMessage = errorMessage;
    }
    
    public void markAsSkipped() {
        this.status = STATUS_SKIPPED;
    }
    
    public boolean hasLocator() {
        return locator != null && !locator.trim().isEmpty();
    }
    
    public boolean hasInputData() {
        return inputData != null && !inputData.trim().isEmpty();
    }
    
    public boolean hasExpectedResult() {
        return expectedResult != null && !expectedResult.trim().isEmpty();
    }
    
    public boolean hasScreenshot() {
        return screenshotPath != null && !screenshotPath.trim().isEmpty();
    }
    
    public String getExecutionTimeFormatted() {
        if (executionTime < 1000) {
            return executionTime + " ms";
        } else {
            return String.format("%.2f s", executionTime / 1000.0);
        }
    }
    
    @Override
    public String toString() {
        return "TestStep{" +
                "stepNumber=" + stepNumber +
                ", action='" + action + '\'' +
                ", description='" + description + '\'' +
                ", status='" + status + '\'' +
                ", executionTime=" + getExecutionTimeFormatted() +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        TestStep testStep = (TestStep) obj;
        return id != null ? id.equals(testStep.id) : testStep.id == null;
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
