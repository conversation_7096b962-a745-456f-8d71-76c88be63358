package com.automation.models;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * Test Case Model for representing test cases
 * Used for AI-generated tests and test management
 */
public class TestCase {
    
    private String id;
    private String name;
    private String description;
    private String category;
    private String priority;
    private String status;
    private List<TestStep> steps;
    private Map<String, String> testData;
    private String expectedResult;
    private String actualResult;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private List<String> tags;
    private String url;
    private int estimatedDuration; // in seconds
    private String preconditions;
    private String postconditions;
    
    public TestCase() {
        this.steps = new ArrayList<>();
        this.testData = new HashMap<>();
        this.tags = new ArrayList<>();
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.status = "CREATED";
    }
    
    public TestCase(String name, String description) {
        this();
        this.name = name;
        this.description = description;
    }
    
    // Getters and Setters
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getPriority() {
        return priority;
    }
    
    public void setPriority(String priority) {
        this.priority = priority;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
        this.updatedAt = LocalDateTime.now();
    }
    
    public List<TestStep> getSteps() {
        return steps;
    }
    
    public void setSteps(List<TestStep> steps) {
        this.steps = steps;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void addStep(TestStep step) {
        this.steps.add(step);
        this.updatedAt = LocalDateTime.now();
    }
    
    public Map<String, String> getTestData() {
        return testData;
    }
    
    public void setTestData(Map<String, String> testData) {
        this.testData = testData;
    }
    
    public void addTestData(String key, String value) {
        this.testData.put(key, value);
    }
    
    public String getExpectedResult() {
        return expectedResult;
    }
    
    public void setExpectedResult(String expectedResult) {
        this.expectedResult = expectedResult;
    }
    
    public String getActualResult() {
        return actualResult;
    }
    
    public void setActualResult(String actualResult) {
        this.actualResult = actualResult;
        this.updatedAt = LocalDateTime.now();
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    public void addTag(String tag) {
        if (!this.tags.contains(tag)) {
            this.tags.add(tag);
        }
    }
    
    public String getUrl() {
        return url;
    }
    
    public void setUrl(String url) {
        this.url = url;
    }
    
    public int getEstimatedDuration() {
        return estimatedDuration;
    }
    
    public void setEstimatedDuration(int estimatedDuration) {
        this.estimatedDuration = estimatedDuration;
    }
    
    public String getPreconditions() {
        return preconditions;
    }
    
    public void setPreconditions(String preconditions) {
        this.preconditions = preconditions;
    }
    
    public String getPostconditions() {
        return postconditions;
    }
    
    public void setPostconditions(String postconditions) {
        this.postconditions = postconditions;
    }
    
    // Utility methods
    
    public boolean isPassed() {
        return "PASSED".equalsIgnoreCase(status);
    }
    
    public boolean isFailed() {
        return "FAILED".equalsIgnoreCase(status);
    }
    
    public boolean isSkipped() {
        return "SKIPPED".equalsIgnoreCase(status);
    }
    
    public int getStepCount() {
        return steps.size();
    }
    
    public boolean hasTag(String tag) {
        return tags.contains(tag);
    }
    
    @Override
    public String toString() {
        return "TestCase{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", status='" + status + '\'' +
                ", stepCount=" + getStepCount() +
                ", tags=" + tags +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        TestCase testCase = (TestCase) obj;
        return id != null ? id.equals(testCase.id) : testCase.id == null;
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
