/* Custom CSS for TestNG Reports - White & Magenta Theme */

/* Main Body */
body {
    background: linear-gradient(135deg, #ffffff 0%, #fdf2f8 50%, #f3e8ff 100%) !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    margin: 0 !important;
    padding: 20px !important;
}

/* Header */
.header {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    color: white !important;
    padding: 20px !important;
    border-radius: 12px !important;
    margin-bottom: 20px !important;
    box-shadow: 0 4px 15px rgba(216, 70, 239, 0.3) !important;
}

.header h1, .header h2 {
    color: white !important;
    margin: 0 !important;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3) !important;
}

/* Navigation */
.navigator-root {
    background: white !important;
    border: 2px solid #f3e8ff !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 15px rgba(216, 70, 239, 0.1) !important;
}

.navigator-suite-header {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    color: white !important;
    padding: 15px !important;
    border-radius: 10px 10px 0 0 !important;
}

.navigator-test-header {
    background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%) !important;
    color: #a855f7 !important;
    padding: 10px !important;
    border-left: 4px solid #d946ef !important;
}

/* Tables */
table {
    background: white !important;
    border: 2px solid #f3e8ff !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 15px rgba(216, 70, 239, 0.1) !important;
    width: 100% !important;
}

th {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    color: white !important;
    padding: 15px !important;
    text-align: left !important;
    font-weight: bold !important;
    border: none !important;
}

td {
    padding: 12px 15px !important;
    border-bottom: 1px solid #f3e8ff !important;
    vertical-align: top !important;
}

tr:nth-child(even) {
    background: #fefefe !important;
}

tr:nth-child(odd) {
    background: white !important;
}

tr:hover {
    background: #fdf2f8 !important;
    transition: background 0.3s ease !important;
}

/* Test Results */
.passed {
    background: linear-gradient(135deg, #10b981 0%, #d946ef 30%) !important;
    color: white !important;
    padding: 5px 10px !important;
    border-radius: 6px !important;
    font-weight: bold !important;
}

.failed {
    background: linear-gradient(135deg, #ef4444 0%, #d946ef 30%) !important;
    color: white !important;
    padding: 5px 10px !important;
    border-radius: 6px !important;
    font-weight: bold !important;
}

.skipped {
    background: linear-gradient(135deg, #f59e0b 0%, #d946ef 30%) !important;
    color: white !important;
    padding: 5px 10px !important;
    border-radius: 6px !important;
    font-weight: bold !important;
}

/* Links */
a {
    color: #d946ef !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    transition: color 0.3s ease !important;
}

a:hover {
    color: #a855f7 !important;
    text-decoration: underline !important;
}

/* Panels and Sections */
.panel {
    background: white !important;
    border: 2px solid #f3e8ff !important;
    border-radius: 12px !important;
    margin: 15px 0 !important;
    box-shadow: 0 4px 15px rgba(216, 70, 239, 0.1) !important;
}

.panel-heading {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    color: white !important;
    padding: 15px !important;
    border-radius: 10px 10px 0 0 !important;
    font-weight: bold !important;
}

.panel-body {
    padding: 20px !important;
}

/* Summary Section */
.summary {
    background: linear-gradient(135deg, #ffffff 0%, #fdf2f8 100%) !important;
    border: 2px solid #e879f9 !important;
    border-radius: 12px !important;
    padding: 20px !important;
    margin: 20px 0 !important;
    box-shadow: 0 4px 15px rgba(216, 70, 239, 0.1) !important;
}

.summary h2 {
    color: #d946ef !important;
    margin-top: 0 !important;
    border-bottom: 2px solid #e879f9 !important;
    padding-bottom: 10px !important;
}

/* Test Details */
.test-detail {
    background: white !important;
    border: 1px solid #f3e8ff !important;
    border-radius: 8px !important;
    margin: 10px 0 !important;
    padding: 15px !important;
    box-shadow: 0 2px 8px rgba(216, 70, 239, 0.1) !important;
}

.test-name {
    color: #d946ef !important;
    font-weight: bold !important;
    font-size: 16px !important;
    margin-bottom: 8px !important;
}

.test-description {
    color: #666 !important;
    font-style: italic !important;
    margin-bottom: 10px !important;
}

/* Method Details */
.method-detail {
    background: #fefefe !important;
    border-left: 4px solid #d946ef !important;
    padding: 10px 15px !important;
    margin: 8px 0 !important;
    border-radius: 0 6px 6px 0 !important;
}

.method-name {
    color: #a855f7 !important;
    font-weight: bold !important;
}

.method-time {
    color: #666 !important;
    font-size: 12px !important;
}

/* Exception Details */
.exception {
    background: #fef2f2 !important;
    border: 1px solid #fecaca !important;
    border-radius: 6px !important;
    padding: 10px !important;
    margin: 10px 0 !important;
    font-family: monospace !important;
    font-size: 12px !important;
    color: #dc2626 !important;
}

/* Statistics */
.stats-table {
    background: white !important;
    border: 2px solid #f3e8ff !important;
    border-radius: 12px !important;
    overflow: hidden !important;
}

.stats-table th {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    color: white !important;
    text-align: center !important;
}

.stats-table td {
    text-align: center !important;
    font-weight: bold !important;
}

/* Progress Bars */
.progress {
    background: #f3e8ff !important;
    border-radius: 8px !important;
    height: 20px !important;
    overflow: hidden !important;
    margin: 10px 0 !important;
}

.progress-bar {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    height: 100% !important;
    transition: width 0.6s ease !important;
}

/* Buttons */
.btn {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    color: white !important;
    border: none !important;
    padding: 8px 16px !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

.btn:hover {
    background: linear-gradient(135deg, #c026d3 0%, #9333ea 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(216, 70, 239, 0.3) !important;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%) !important;
    border-top: 3px solid #e879f9 !important;
    color: #a855f7 !important;
    padding: 20px !important;
    text-align: center !important;
    margin-top: 30px !important;
    border-radius: 12px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px !important;
    }
    
    .header {
        padding: 15px !important;
    }
    
    table {
        font-size: 12px !important;
    }
    
    th, td {
        padding: 8px !important;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px !important;
}

::-webkit-scrollbar-track {
    background: #f3e8ff !important;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    border-radius: 4px !important;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #c026d3 0%, #9333ea 100%) !important;
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.panel, .test-detail, .summary {
    animation: fadeIn 0.5s ease-out !important;
}

/* Print Styles */
@media print {
    body {
        background: white !important;
    }
    
    .header {
        background: #d946ef !important;
        -webkit-print-color-adjust: exact !important;
    }
    
    .panel-heading, th {
        background: #d946ef !important;
        -webkit-print-color-adjust: exact !important;
    }
}
