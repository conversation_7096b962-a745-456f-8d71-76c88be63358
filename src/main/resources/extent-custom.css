/* Custom CSS for ExtentReports - White & Magenta Theme */

/* Main Background */
body {
    background: linear-gradient(135deg, #ffffff 0%, #fdf2f8 50%, #f3e8ff 100%) !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

/* Header Styling */
.navbar-brand, .navbar {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    border-bottom: 3px solid #e879f9 !important;
}

.navbar-brand {
    color: white !important;
    font-weight: bold !important;
}

/* Navigation Pills */
.nav-pills > li.active > a, .nav-pills > li.active > a:hover, .nav-pills > li.active > a:focus {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    color: white !important;
}

.nav-pills > li > a {
    color: #a855f7 !important;
    border-radius: 8px !important;
    margin: 2px !important;
}

.nav-pills > li > a:hover {
    background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%) !important;
    color: #d946ef !important;
}

/* Cards and Panels */
.panel, .card {
    background: white !important;
    border: 2px solid #f3e8ff !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 15px rgba(216, 70, 239, 0.1) !important;
}

.panel-heading, .card-header {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    color: white !important;
    border-radius: 10px 10px 0 0 !important;
    border-bottom: none !important;
}

/* Test Status Colors */
.test-pass {
    background: linear-gradient(135deg, #10b981 0%, #d946ef 30%) !important;
    color: white !important;
}

.test-fail {
    background: linear-gradient(135deg, #ef4444 0%, #d946ef 30%) !important;
    color: white !important;
}

.test-skip {
    background: linear-gradient(135deg, #f59e0b 0%, #d946ef 30%) !important;
    color: white !important;
}

/* Progress Bars */
.progress-bar {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
}

.progress {
    background: #f3e8ff !important;
    border-radius: 8px !important;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    border: none !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(216, 70, 239, 0.3) !important;
    transition: all 0.3s ease !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #c026d3 0%, #9333ea 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(216, 70, 239, 0.4) !important;
}

.btn-secondary {
    background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%) !important;
    color: #a855f7 !important;
    border: 2px solid #e879f9 !important;
    border-radius: 8px !important;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    color: white !important;
}

/* Tables */
.table {
    background: white !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 2px 10px rgba(216, 70, 239, 0.1) !important;
}

.table thead th {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    color: white !important;
    border: none !important;
}

.table tbody tr:hover {
    background: #fdf2f8 !important;
}

.table tbody tr:nth-child(even) {
    background: #fefefe !important;
}

.table tbody tr:nth-child(odd) {
    background: white !important;
}

/* Sidebar */
.sidebar {
    background: linear-gradient(135deg, #ffffff 0%, #fdf2f8 100%) !important;
    border-right: 3px solid #f3e8ff !important;
}

/* Test Details */
.test-detail {
    background: white !important;
    border: 1px solid #f3e8ff !important;
    border-radius: 8px !important;
    margin: 10px 0 !important;
    padding: 15px !important;
}

/* Screenshots */
.screenshot {
    border: 3px solid #e879f9 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(216, 70, 239, 0.2) !important;
}

/* Logs */
.log-container {
    background: #fefefe !important;
    border: 1px solid #f3e8ff !important;
    border-radius: 8px !important;
    padding: 10px !important;
}

.log-pass {
    color: #10b981 !important;
    font-weight: bold !important;
}

.log-fail {
    color: #ef4444 !important;
    font-weight: bold !important;
}

.log-info {
    color: #a855f7 !important;
}

/* Charts */
.chart-container {
    background: white !important;
    border: 2px solid #f3e8ff !important;
    border-radius: 12px !important;
    padding: 20px !important;
    box-shadow: 0 4px 15px rgba(216, 70, 239, 0.1) !important;
}

/* Dashboard Cards */
.dashboard-card {
    background: linear-gradient(135deg, #ffffff 0%, #fdf2f8 100%) !important;
    border: 2px solid #e879f9 !important;
    border-radius: 12px !important;
    padding: 20px !important;
    margin: 10px !important;
    box-shadow: 0 4px 15px rgba(216, 70, 239, 0.1) !important;
    transition: all 0.3s ease !important;
}

.dashboard-card:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 25px rgba(216, 70, 239, 0.2) !important;
}

/* Status Badges */
.badge-pass {
    background: linear-gradient(135deg, #10b981 0%, #d946ef 50%) !important;
    color: white !important;
}

.badge-fail {
    background: linear-gradient(135deg, #ef4444 0%, #d946ef 50%) !important;
    color: white !important;
}

.badge-skip {
    background: linear-gradient(135deg, #f59e0b 0%, #d946ef 50%) !important;
    color: white !important;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%) !important;
    border-top: 3px solid #e879f9 !important;
    color: #a855f7 !important;
    padding: 20px !important;
    text-align: center !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px !important;
}

::-webkit-scrollbar-track {
    background: #f3e8ff !important;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important;
    border-radius: 4px !important;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #c026d3 0%, #9333ea 100%) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-card {
        margin: 5px !important;
        padding: 15px !important;
    }
    
    .btn {
        padding: 8px 16px !important;
        font-size: 14px !important;
    }
}

/* Animation for loading */
@keyframes magentaPulse {
    0% { box-shadow: 0 0 0 0 rgba(216, 70, 239, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(216, 70, 239, 0); }
    100% { box-shadow: 0 0 0 0 rgba(216, 70, 239, 0); }
}

.pulse-magenta {
    animation: magentaPulse 2s infinite !important;
}
