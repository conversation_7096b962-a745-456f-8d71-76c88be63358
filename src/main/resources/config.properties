# Playwright Automation Framework Configuration
# Default configuration for all environments

# Browser Configuration
browser=chromium
headless=false
browser.args=--disable-web-security,--disable-features=VizDisplayCompositor,--no-sandbox

# Application URLs
base.url=https://example.com
api.base.url=https://api.example.com

# Timeouts (in milliseconds)
default.timeout=30000
page.load.timeout=60000
element.timeout=10000
api.timeout=30000

# Test Data Configuration
test.data.path=src/test/resources/testdata
test.data.format=json

# Reporting Configuration
reports.path=test-results/reports
screenshots.path=test-results/screenshots
videos.path=test-results/videos

# Screenshot Configuration
screenshot.on.failure=true
screenshot.on.success=false
screenshot.retention.days=7
screenshot.format=png
screenshot.quality=90

# Video Recording
video.recording=false
video.retention.days=3

# Parallel Execution
parallel.tests=true
thread.count=3
retry.failed.tests=true
max.retry.count=2

# Database Configuration (if needed)
database.url=
database.username=
database.password=
database.driver=

# API Configuration
api.key=
api.timeout=30000
api.retry.count=3

# AI/LLM Configuration
openai.api.key=
augment.api.key=
augment.api.url=https://api.augmentcode.com
augment.enabled=false
augment.timeout=30
augment.fallback.enabled=true
augment.analytics.enabled=true
augment.smart.locators.enabled=true

# Test Generation Configuration
test.generation.enabled=true
test.generation.model=gpt-4
test.generation.temperature=0.3
test.generation.max.tokens=2000

# Logging Configuration
log.level=INFO
log.file.path=test-results/logs/automation.log
log.console.enabled=true

# Environment Specific
environment=dev

# Performance Configuration
performance.monitoring=false
performance.threshold.page.load=5000
performance.threshold.api.response=2000

# Security Configuration
security.testing=false
security.headers.check=true

# Accessibility Configuration
accessibility.testing=false
accessibility.standards=WCAG2AA

# Cross-browser Testing
cross.browser.testing=false
browsers.list=chromium,firefox,webkit

# Mobile Testing
mobile.testing=false
mobile.devices=iPhone 12,Samsung Galaxy S21,iPad

# Grid Configuration (for Selenium Grid if needed)
grid.enabled=false
grid.hub.url=http://localhost:4444/wd/hub

# Docker Configuration
docker.enabled=false
docker.image=mcr.microsoft.com/playwright:latest

# CI/CD Configuration
ci.environment=false
ci.build.number=
ci.branch.name=

# Notification Configuration
notifications.enabled=false
notifications.email=
notifications.slack.webhook=
notifications.teams.webhook=

# Test Management Integration
test.management.enabled=false
test.management.tool=
test.management.project.id=
test.management.api.key=

# Custom Properties
custom.property.1=
custom.property.2=
custom.property.3=
