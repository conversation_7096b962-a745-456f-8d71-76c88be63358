# Production Environment Configuration
# Overrides default configuration for production environment

# Application URLs
base.url=https://www.example.com
api.base.url=https://api.example.com

# Browser Configuration
browser=chromium
headless=true
browser.args=--disable-web-security,--disable-features=VizDisplayCompositor,--no-sandbox,--disable-dev-shm-usage

# Timeouts (optimized for production)
default.timeout=25000
page.load.timeout=45000
element.timeout=8000
api.timeout=25000

# Test Data Configuration
test.data.path=src/test/resources/testdata/prod
test.data.format=json

# Reporting Configuration
reports.path=test-results/prod/reports
screenshots.path=test-results/prod/screenshots
videos.path=test-results/prod/videos

# Screenshot Configuration
screenshot.on.failure=true
screenshot.on.success=false
screenshot.retention.days=7
screenshot.format=png
screenshot.quality=80

# Video Recording
video.recording=false
video.retention.days=3

# Parallel Execution
parallel.tests=true
thread.count=5
retry.failed.tests=true
max.retry.count=1

# Database Configuration (Production DB - Read Only)
database.url=*****************************************************
database.username=readonly_user
database.password=readonly_password
database.driver=com.mysql.cj.jdbc.Driver

# API Configuration
api.key=prod_api_key_secure
api.timeout=25000
api.retry.count=2

# AI/LLM Configuration
openai.api.key=
augment.api.key=
augment.base.url=https://api.augmentcode.com

# Test Generation Configuration
test.generation.enabled=false
test.generation.model=gpt-4
test.generation.temperature=0.2
test.generation.max.tokens=1000

# Logging Configuration
log.level=WARN
log.file.path=test-results/prod/logs/automation.log
log.console.enabled=false

# Environment Specific
environment=prod

# Performance Configuration
performance.monitoring=true
performance.threshold.page.load=4000
performance.threshold.api.response=2000

# Security Configuration
security.testing=true
security.headers.check=true

# Accessibility Configuration
accessibility.testing=true
accessibility.standards=WCAG2AA

# Cross-browser Testing
cross.browser.testing=true
browsers.list=chromium,firefox,webkit

# Mobile Testing
mobile.testing=true
mobile.devices=iPhone 12,Samsung Galaxy S21,iPad

# Grid Configuration
grid.enabled=true
grid.hub.url=http://prod-grid.example.com:4444/wd/hub

# Docker Configuration
docker.enabled=true
docker.image=mcr.microsoft.com/playwright:latest

# CI/CD Configuration
ci.environment=true
ci.build.number=${BUILD_NUMBER}
ci.branch.name=${BRANCH_NAME}

# Notification Configuration
notifications.enabled=true
notifications.email=<EMAIL>
notifications.slack.webhook=https://hooks.slack.com/services/prod/webhook
notifications.teams.webhook=https://outlook.office.com/webhook/prod

# Test Management Integration
test.management.enabled=true
test.management.tool=jira
test.management.project.id=PROD-PROJECT
test.management.api.key=prod_jira_api_key

# Production Specific Properties
prod.smoke.tests.only=true
prod.data.modification=false
prod.external.services=true
prod.ssl.verification=true
prod.proxy.enabled=true
prod.proxy.host=prod-proxy.example.com
prod.proxy.port=8080

# Monitoring and Alerting
monitoring.enabled=true
monitoring.health.checks=true
monitoring.performance.alerts=true
monitoring.error.threshold=5

# Compliance and Audit
compliance.logging=true
audit.trail=true
data.privacy.checks=true

# Rate Limiting
rate.limiting.enabled=true
rate.limiting.requests.per.minute=60
rate.limiting.burst.size=10

# Custom Properties for Production
custom.property.1=prod_value_1
custom.property.2=prod_value_2
custom.property.3=prod_value_3
