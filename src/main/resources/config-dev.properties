# Development Environment Configuration
# Overrides default configuration for development environment

# Application URLs
base.url=https://dev.example.com
api.base.url=https://dev-api.example.com

# Browser Configuration
browser=chromium
headless=false
browser.args=--disable-web-security,--disable-features=VizDisplayCompositor,--no-sandbox,--disable-dev-shm-usage

# Timeouts (more lenient for development)
default.timeout=45000
page.load.timeout=90000
element.timeout=15000
api.timeout=45000

# Test Data Configuration
test.data.path=src/test/resources/testdata/dev
test.data.format=json

# Reporting Configuration
reports.path=test-results/dev/reports
screenshots.path=test-results/dev/screenshots
videos.path=test-results/dev/videos

# Screenshot Configuration
screenshot.on.failure=true
screenshot.on.success=true
screenshot.retention.days=14
screenshot.format=png
screenshot.quality=100

# Video Recording
video.recording=true
video.retention.days=7

# Parallel Execution
parallel.tests=false
thread.count=1
retry.failed.tests=true
max.retry.count=3

# Database Configuration (Development DB)
database.url=*******************************************
database.username=dev_user
database.password=dev_password
database.driver=com.mysql.cj.jdbc.Driver

# API Configuration
api.key=dev_api_key_12345
api.timeout=45000
api.retry.count=5

# AI/LLM Configuration
openai.api.key=
augment.api.key=
augment.base.url=https://dev-api.augmentcode.com

# Test Generation Configuration
test.generation.enabled=true
test.generation.model=gpt-3.5-turbo
test.generation.temperature=0.5
test.generation.max.tokens=1500

# Logging Configuration
log.level=DEBUG
log.file.path=test-results/dev/logs/automation.log
log.console.enabled=true

# Environment Specific
environment=dev

# Performance Configuration
performance.monitoring=true
performance.threshold.page.load=8000
performance.threshold.api.response=3000

# Security Configuration
security.testing=true
security.headers.check=true

# Accessibility Configuration
accessibility.testing=true
accessibility.standards=WCAG2AA

# Cross-browser Testing
cross.browser.testing=true
browsers.list=chromium,firefox

# Mobile Testing
mobile.testing=true
mobile.devices=iPhone 12,Samsung Galaxy S21

# Grid Configuration
grid.enabled=false
grid.hub.url=http://dev-grid.example.com:4444/wd/hub

# Docker Configuration
docker.enabled=false
docker.image=mcr.microsoft.com/playwright:latest

# CI/CD Configuration
ci.environment=false
ci.build.number=
ci.branch.name=develop

# Notification Configuration
notifications.enabled=true
notifications.email=<EMAIL>
notifications.slack.webhook=https://hooks.slack.com/services/dev/webhook
notifications.teams.webhook=

# Test Management Integration
test.management.enabled=true
test.management.tool=jira
test.management.project.id=DEV-PROJECT
test.management.api.key=dev_jira_api_key

# Development Specific Properties
dev.debug.mode=true
dev.mock.services=true
dev.test.data.reset=true
dev.database.reset=true
dev.cache.disabled=true
dev.ssl.verification=false
dev.proxy.enabled=false
dev.proxy.host=
dev.proxy.port=

# Custom Properties for Development
custom.property.1=dev_value_1
custom.property.2=dev_value_2
custom.property.3=dev_value_3
