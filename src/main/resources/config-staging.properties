# Staging Environment Configuration
# Overrides default configuration for staging environment

# Application URLs
base.url=https://staging.example.com
api.base.url=https://staging-api.example.com

# Browser Configuration
browser=chromium
headless=true
browser.args=--disable-web-security,--disable-features=VizDisplayCompositor,--no-sandbox,--disable-dev-shm-usage

# Timeouts (production-like)
default.timeout=30000
page.load.timeout=60000
element.timeout=10000
api.timeout=30000

# Test Data Configuration
test.data.path=src/test/resources/testdata/staging
test.data.format=json

# Reporting Configuration
reports.path=test-results/staging/reports
screenshots.path=test-results/staging/screenshots
videos.path=test-results/staging/videos

# Screenshot Configuration
screenshot.on.failure=true
screenshot.on.success=false
screenshot.retention.days=10
screenshot.format=png
screenshot.quality=90

# Video Recording
video.recording=false
video.retention.days=5

# Parallel Execution
parallel.tests=true
thread.count=3
retry.failed.tests=true
max.retry.count=2

# Database Configuration (Staging DB)
database.url=***********************************************
database.username=staging_user
database.password=staging_password
database.driver=com.mysql.cj.jdbc.Driver

# API Configuration
api.key=staging_api_key_67890
api.timeout=30000
api.retry.count=3

# AI/LLM Configuration
openai.api.key=
augment.api.key=
augment.base.url=https://staging-api.augmentcode.com

# Test Generation Configuration
test.generation.enabled=true
test.generation.model=gpt-4
test.generation.temperature=0.3
test.generation.max.tokens=2000

# Logging Configuration
log.level=INFO
log.file.path=test-results/staging/logs/automation.log
log.console.enabled=true

# Environment Specific
environment=staging

# Performance Configuration
performance.monitoring=true
performance.threshold.page.load=6000
performance.threshold.api.response=2500

# Security Configuration
security.testing=true
security.headers.check=true

# Accessibility Configuration
accessibility.testing=true
accessibility.standards=WCAG2AA

# Cross-browser Testing
cross.browser.testing=true
browsers.list=chromium,firefox,webkit

# Mobile Testing
mobile.testing=true
mobile.devices=iPhone 12,Samsung Galaxy S21,iPad

# Grid Configuration
grid.enabled=true
grid.hub.url=http://staging-grid.example.com:4444/wd/hub

# Docker Configuration
docker.enabled=true
docker.image=mcr.microsoft.com/playwright:latest

# CI/CD Configuration
ci.environment=true
ci.build.number=${BUILD_NUMBER}
ci.branch.name=${BRANCH_NAME}

# Notification Configuration
notifications.enabled=true
notifications.email=<EMAIL>
notifications.slack.webhook=https://hooks.slack.com/services/staging/webhook
notifications.teams.webhook=https://outlook.office.com/webhook/staging

# Test Management Integration
test.management.enabled=true
test.management.tool=jira
test.management.project.id=STAGING-PROJECT
test.management.api.key=staging_jira_api_key

# Staging Specific Properties
staging.smoke.tests.only=false
staging.data.refresh=true
staging.external.services=true
staging.ssl.verification=true
staging.proxy.enabled=false
staging.proxy.host=
staging.proxy.port=

# Load Testing Configuration
load.testing.enabled=false
load.testing.users=10
load.testing.duration=300

# Integration Testing
integration.testing.enabled=true
integration.external.apis=true
integration.database.checks=true

# Custom Properties for Staging
custom.property.1=staging_value_1
custom.property.2=staging_value_2
custom.property.3=staging_value_3
