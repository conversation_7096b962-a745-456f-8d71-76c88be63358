{
  "id": "93be14da-7c43-4da1-bc73-06cbe971b9cc",
  "name": "testNavigateJeevansathiClickLoginButton",
  "description": "Navigate to <PERSON><PERSON><PERSON><PERSON><PERSON>, click login button, enter username '<EMAIL>', enter password 'TestPass123', click submit button, verify dashboard appears, check user profile link is visible",
  "module": "login",
  "category": "functional",
  "priority": "null",
  "url": "https://www.jeevansathi.com/",
  "tags": ["generated", "rule-based", "login", "auto-generated"],
  "steps": [
    {
      "stepNumber": 1,
      "action": "NAVIGATE",
      "description": "Navigate to Jeevansathi,",
      "inputData": "Jeevansathi,",
      "expectedResult": "Page should load successfully"
    },
    {
      "stepNumber": 2,
      "action": "CLICK",
      "description": "Click on submit button, verify dashboard appears, check user profile link is visible",
      "locator": "button:has-text("submit , verify dashboard appears, check user profile is visible")",
      "expectedResult": "Element should be clicked successfully"
    }
  ],
  "expectedResult": "null",
  "estimatedDuration": 20,
  "createdBy": "AI Generator",
  "createdAt": "Sun Jun 15 13:41:42 IST 2025"
}