{"id": "6f0cde8d-cffa-43c8-ade2-5fe8672a799d", "name": "testNavigateJeevansathigotoFooterAndVerify", "description": "Navigate to <PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON> Footer and verify all the links available under the Legal columns if its redirecting and content visible,use HelpAndSupportLogic class itself along with iots verification class", "module": "footerFile", "category": "functional", "priority": "null", "url": "https://www.jeevansathi.com/", "tags": ["generated", "rule-based", "footerFile", "auto-generated"], "steps": [{"stepNumber": 1, "action": "NAVIGATE", "description": "Navigate to jeevansathi,Goto", "inputData": "<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>", "expectedResult": "Page should load successfully"}, {"stepNumber": 2, "action": "VERIFY", "description": "Verify all the links available under the Legal columns if its redirecting", "expectedResult": "all the links available under the Legal columns if its redirecting"}, {"stepNumber": 3, "action": "MANUAL", "description": "content visible,use HelpAndSupportLogic class itself along with iots verification class", "expectedResult": "Step should be completed manually"}], "expectedResult": "null", "estimatedDuration": 30, "createdBy": "AI Generator", "createdAt": "Mon Jun 16 13:45:50 IST 2025"}