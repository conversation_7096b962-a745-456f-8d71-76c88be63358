{
  "id": "196e3770-6c56-490d-93f7-737872d0d27c",
  "name": "testNavigateJeevansathiClickRegisterButton",
  "description": "Navigate to Jeevans<PERSON>i, click register button, fill name field with 'Test User', enter email '<EMAIL>', set password 'TestPass123', select age 25, choose location Mumbai, click submit, verify registration success message",
  "module": "registration",
  "category": "functional",
  "priority": "null",
  "url": "https://www.jeevansathi.com/",
  "tags": ["generated", "rule-based", "registration", "auto-generated"],
  "steps": [
    {
      "stepNumber": 1,
      "action": "NAVIGATE",
      "description": "Navigate to Jeevansathi,",
      "inputData": "Jeevans<PERSON><PERSON>,",
      "expectedResult": "Page should load successfully"
    },
    {
      "stepNumber": 2,
      "action": "CLICK",
      "description": "Click on submit, verify registration success message",
      "locator": "*:has-text("submit, verify registration success message")",
      "expectedResult": "Element should be clicked successfully"
    }
  ],
  "expectedResult": "null",
  "estimatedDuration": 20,
  "createdBy": "AI Generator",
  "createdAt": "Sun Jun 15 13:41:42 IST 2025"
}