package com.automation.logic;

import com.automation.core.TestBase;
import com.automation.generator.UniversalPromptProcessor;
import com.automation.models.TestCase;
import com.automation.models.TestStep;
import com.automation.utils.ActionUtility;
import com.automation.verification.JeevansathiVerification;
import org.testng.annotations.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * Universal Test Executor - Executes ANY test generated from ANY prompt
 * Dynamically processes all prompt files and executes generated tests
 */
public class UniversalTestExecutor extends TestBase {
    
    private static final Logger logger = LoggerFactory.getLogger(UniversalTestExecutor.class);
    
    private final UniversalPromptProcessor promptProcessor;
    private final JeevansathiVerification verification;
    private ActionUtility actionUtility;
    
    public UniversalTestExecutor() {
        this.promptProcessor = new UniversalPromptProcessor();
        this.verification = new JeevansathiVerification();
    }
    
    /**
     * Main test method - Processes ALL pending prompts and executes generated tests
     */
    @Test(description = "Universal Prompt-Based Test Execution",
          groups = {"universal", "prompt-based", "dynamic"}, priority = 1)
    public void executeAllPromptBasedTests() {
        
        logTestInfo("🚀 Starting Universal Prompt-Based Test Execution");
        
        // Initialize ActionUtility
        if (actionUtility == null) {
            actionUtility = new ActionUtility(getPage());
        }
        
        // Get processing statistics
        Map<String, Integer> stats = promptProcessor.getProcessingStats();
        logTestInfo("📊 Prompt Statistics:");
        logTestInfo("   📋 Pending prompts: " + stats.get("pending"));
        logTestInfo("   ✅ Processed prompts: " + stats.get("processed"));
        logTestInfo("   🧪 Generated tests: " + stats.get("generated"));
        
        if (stats.get("pending") == 0) {
            logTestInfo("⚠️ No pending prompts found. Please add .txt files to src/test/resources/prompts/pending/");
            logTestInfo("💡 Example: Create 'login_test.txt' with your test scenario");
            return;
        }
        
        try {
            // Process all pending prompts
            List<TestCase> allTestCases = promptProcessor.processAllPendingPrompts();
            
            if (allTestCases.isEmpty()) {
                logTestInfo("⚠️ No test cases were generated from prompts");
                return;
            }
            
            logTestInfo("✅ Generated " + allTestCases.size() + " test cases from prompts");
            
            // Execute all generated test cases
            int passedTests = 0;
            int failedTests = 0;
            
            for (int i = 0; i < allTestCases.size(); i++) {
                TestCase testCase = allTestCases.get(i);
                
                logTestInfo("🧪 Executing Test Case " + (i + 1) + "/" + allTestCases.size() + ": " + testCase.getName());
                logTestInfo("📝 Description: " + testCase.getDescription());
                logTestInfo("🏷️ Module: " + testCase.getModule());
                logTestInfo("📂 Category: " + testCase.getCategory());
                
                try {
                    boolean testResult = executeGeneratedTestCase(testCase);
                    if (testResult) {
                        passedTests++;
                        logTestPass("✅ Test Case PASSED: " + testCase.getName());
                    } else {
                        failedTests++;
                        logTestFail("❌ Test Case FAILED: " + testCase.getName());
                    }
                } catch (Exception e) {
                    failedTests++;
                    logTestFail("💥 Test Case ERROR: " + testCase.getName() + " - " + e.getMessage());
                    captureScreenshot("test_error_" + testCase.getName());
                }
                
                // Small delay between tests
                actionUtility.waitFor(2000);
            }
            
            // Final summary
            logTestInfo("🎯 Universal Test Execution Summary:");
            logTestInfo("   📊 Total Tests: " + allTestCases.size());
            logTestInfo("   ✅ Passed: " + passedTests);
            logTestInfo("   ❌ Failed: " + failedTests);
            logTestInfo("   📈 Success Rate: " + (passedTests * 100 / allTestCases.size()) + "%");
            
            captureScreenshot("universal_test_execution_complete");
            logTestPass("🎉 Universal prompt-based test execution completed!");
            
        } catch (Exception e) {
            logTestFail("💥 Universal test execution failed: " + e.getMessage());
            captureScreenshot("universal_execution_failure");
            throw e;
        }
    }
    
    /**
     * Execute a single generated test case
     */
    private boolean executeGeneratedTestCase(TestCase testCase) {
        logTestInfo("🚀 Executing steps for: " + testCase.getName());
        
        boolean allStepsSuccessful = true;
        
        for (TestStep step : testCase.getSteps()) {
            logTestInfo("📋 Step " + step.getStepNumber() + ": " + step.getDescription());
            
            try {
                boolean stepResult = executeTestStep(step);
                if (stepResult) {
                    logTestPass("   ✅ Step completed successfully");
                } else {
                    logTestInfo("   ⚠️ Step completed with issues");
                    allStepsSuccessful = false;
                }
            } catch (Exception e) {
                logTestInfo("   ❌ Step failed: " + e.getMessage());
                allStepsSuccessful = false;
                // Continue with next step instead of failing entire test
            }
        }
        
        return allStepsSuccessful;
    }
    
    /**
     * Execute a single test step
     */
    private boolean executeTestStep(TestStep step) {
        switch (step.getAction().toUpperCase()) {
            case "NAVIGATE":
                if (step.getInputData() != null) {
                    actionUtility.navigateTo(step.getInputData());
                    actionUtility.waitFor(3000); // Wait for page load
                    return true;
                }
                break;
                
            case "CLICK":
                if (step.getLocator() != null) {
                    boolean clicked = actionUtility.clickElement(step.getLocator());
                    if (!clicked) {
                        // Try fallback strategies
                        return tryFallbackClick(step.getDescription());
                    }
                    return clicked;
                }
                break;
                
            case "TYPE":
            case "FILL":
                if (step.getLocator() != null && step.getInputData() != null) {
                    return actionUtility.fillInput(step.getLocator(), step.getInputData());
                }
                break;
                
            case "SELECT":
                if (step.getLocator() != null && step.getInputData() != null) {
                    return actionUtility.selectOption(step.getLocator(), step.getInputData());
                }
                break;
                
            case "VERIFY":
            case "ASSERT":
                if (step.getLocator() != null) {
                    boolean elementPresent = actionUtility.isElementPresent(step.getLocator());
                    verification.softVerify(elementPresent,
                        "✅ Verified: " + step.getDescription(),
                        "❌ Verification failed: " + step.getDescription());
                    return elementPresent;
                } else {
                    // Generic verification based on description
                    return performGenericVerification(step.getDescription());
                }
                
            case "WAIT":
                int waitTime = 2000; // Default 2 seconds
                if (step.getInputData() != null) {
                    try {
                        waitTime = Integer.parseInt(step.getInputData()) * 1000;
                    } catch (NumberFormatException ignored) {}
                }
                actionUtility.waitFor(waitTime);
                return true;
                
            default:
                logTestInfo("⚠️ Unknown action: " + step.getAction() + " - executing as manual step");
                return performManualStep(step);
        }
        
        return false;
    }
    
    /**
     * Try fallback click strategies
     */
    private boolean tryFallbackClick(String description) {
        String desc = description.toLowerCase();
        
        // Common element patterns
        String[] fallbackSelectors = {};
        
        if (desc.contains("login")) {
            fallbackSelectors = new String[]{
                "button:has-text('Login')", "a:has-text('Login')", 
                "input[value*='Login']", "text=/login/i"
            };
        } else if (desc.contains("search")) {
            fallbackSelectors = new String[]{
                "button:has-text('Search')", "input[type='submit']",
                ".search-button", "#search-btn"
            };
        } else if (desc.contains("register") || desc.contains("signup")) {
            fallbackSelectors = new String[]{
                "button:has-text('Register')", "a:has-text('Sign Up')",
                "button:has-text('Join')", "text=/register/i"
            };
        } else if (desc.contains("profile")) {
            fallbackSelectors = new String[]{
                ".profile-card", ".profile-item", "a[href*='profile']",
                ".user-profile", ".member-profile"
            };
        }
        
        // Try each fallback selector
        for (String selector : fallbackSelectors) {
            if (actionUtility.clickElement(selector)) {
                logTestPass("✅ Fallback click succeeded with: " + selector);
                return true;
            }
        }
        
        logTestInfo("⚠️ No fallback strategy worked for: " + description);
        return false;
    }
    
    /**
     * Perform generic verification based on description
     */
    private boolean performGenericVerification(String description) {
        String desc = description.toLowerCase();
        
        if (desc.contains("page") && desc.contains("load")) {
            return actionUtility.getPageTitle().length() > 0;
        } else if (desc.contains("dashboard") || desc.contains("home")) {
            return actionUtility.isElementPresent(".dashboard") || 
                   actionUtility.isElementPresent(".home-content");
        } else if (desc.contains("profile")) {
            return actionUtility.isElementPresent(".profile") ||
                   actionUtility.isElementPresent(".user-profile");
        } else if (desc.contains("search") && desc.contains("result")) {
            return actionUtility.isElementPresent(".search-results") ||
                   actionUtility.isElementPresent(".results-container");
        }
        
        // Generic verification - check if page title changed
        return actionUtility.getPageTitle().length() > 0;
    }
    
    /**
     * Perform manual step (for unrecognized actions)
     */
    private boolean performManualStep(TestStep step) {
        logTestInfo("🔧 Manual step: " + step.getDescription());
        
        // Take screenshot for manual verification
        actionUtility.takeScreenshot("manual_step_" + step.getStepNumber());
        
        // Wait a bit for any dynamic content
        actionUtility.waitFor(2000);
        
        return true; // Assume manual steps are successful
    }
    
    /**
     * Utility method to capture screenshot with test context
     */
    private void captureScreenshot(String context) {
        actionUtility.takeScreenshot(context);
        logTestInfo("📸 Screenshot captured: " + context);
    }
}
