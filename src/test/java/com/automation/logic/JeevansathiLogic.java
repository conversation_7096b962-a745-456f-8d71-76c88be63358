package com.automation.logic;

import com.automation.core.TestBase;
import com.automation.verification.JeevansathiVerification;
import com.automation.generator.TestGenerator;
import com.automation.models.TestCase;
import com.automation.models.TestStep;
import com.automation.integration.AugmentIntegration;
import com.automation.pages.JeevansathiHomePage;
import com.automation.pages.JeevansathiLoginPage;
import com.microsoft.playwright.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.Test;
import java.util.List;

/**
 * Jeevansathi Logic Class - Business logic, actions, and test methods
 */
public class JeevansathiLogic extends TestBase {

    private static final Logger logger = LoggerFactory.getLogger(JeevansathiLogic.class);
    private final String BASE_URL = "https://www.jeevansathi.com/";
    private final JeevansathiVerification verification = new JeevansathiVerification();
    private final TestGenerator testGenerator = new TestGenerator();
    private final AugmentIntegration augment = new AugmentIntegration();

    // Page Objects
    private JeevansathiHomePage homePage;
    private JeevansathiLoginPage loginPage;

    // Initialize page objects
    private void initializePageObjects() {
        if (homePage == null) {
            homePage = new JeevansathiHomePage(getPage());
        }
        if (loginPage == null) {
            loginPage = new JeevansathiLoginPage(getPage());
        }
    }

    // Navigate to Jeevansathi homepage using page object
    public void navigateToHomepage() {
        initializePageObjects();
        logTestInfo("🌐 Navigating to Jeevansathi homepage using page object");
        homePage.navigateToHomepage().waitForHomepageLoad();
        logTestPass("✅ Successfully navigated to Jeevansathi homepage");
    }

    // Click on Login button using page object
    public boolean clickLoginButton() {
        initializePageObjects();
        logTestInfo("🔘 Attempting to click Login button using page object");

        boolean result = homePage.clickLoginButton();

        if (result) {
            logTestPass("✅ Successfully clicked Login button using page object");
        } else {
            logTestFail("❌ Could not click Login button using page object");
        }

        return result;
    }

    // Check if Register button is displayed using page object
    public boolean isRegisterButtonDisplayed() {
        initializePageObjects();
        logTestInfo("🔍 Checking if Register button is displayed using page object");

        // First check on homepage
        boolean result = homePage.isRegisterButtonDisplayed();

        // If not found on homepage, check on login page
        if (!result) {
            result = loginPage.isRegisterButtonDisplayed();
        }

        if (result) {
            logTestPass("✅ Register button found using page object");
        } else {
            logTestFail("❌ Register button not found using page object");
        }

        return result;
    }

    // Click on Forgot Password link using page object
    public boolean clickForgotPasswordLink() {
        initializePageObjects();
        logTestInfo("🔗 Attempting to click Forgot Password link using page object");

        boolean result = loginPage.clickForgotPasswordLink();

        if (result) {
            logTestPass("✅ Successfully clicked Forgot Password using page object");
        } else {
            logTestFail("❌ Could not click Forgot Password using page object");
        }

        return result;
    }

    // Check if Get OTP button is appearing using page object
    public boolean isGetOTPButtonAppearing() {
        initializePageObjects();
        logTestInfo("📱 Checking if Get OTP button is appearing using page object");

        boolean result = loginPage.isGetOTPButtonAppearing();

        if (result) {
            logTestPass("✅ Get OTP button found using page object");
        } else {
            logTestFail("❌ Get OTP button not found using page object");
        }

        return result;
    }
    
    public String getPageTitle() {
        String title = getPage().title();
        logTestInfo("📄 Page title: " + title);
        return title;
    }
    
    // Check if page is loaded properly using page object
    public boolean isPageLoaded() {
        initializePageObjects();

        try {
            boolean isLoaded = homePage.isHomepageLoaded();

            if (isLoaded) {
                logTestPass("✅ Page loaded successfully using page object");
            } else {
                logTestFail("❌ Page may not have loaded correctly using page object");
            }

            return isLoaded;
        } catch (Exception e) {
            logTestFail("❌ Page loading failed: " + e.getMessage());
            return false;
        }
    }
    
    public void captureScreenshot(String message) {
        takeScreenshot(message);
        logTestInfo("📸 Screenshot captured: " + message);
    }
    
    public void waitFor(int milliseconds) {
        getPage().waitForTimeout(milliseconds);
        logTestInfo("⏳ Waited for " + milliseconds + "ms");
    }
    
    public boolean isElementPresent(String selector) {
        try {
            return getPage().locator(selector).count() > 0;
        } catch (Exception e) {
            return false;
        }
    }
    
    public String getElementText(String selector) {
        try {
            return getPage().locator(selector).first().textContent();
        } catch (Exception e) {
            logTestInfo("Could not get text for selector: " + selector);
            return "";
        }
    }
    
    /**
     * Click element by selector
     * @param selector element selector
     * @return true if clicked successfully
     */
    public boolean clickElement(String selector) {
        try {
            getPage().click(selector);
            logTestPass("✅ Clicked element: " + selector);
            return true;
        } catch (Exception e) {
            logTestFail("❌ Failed to click element: " + selector + " - " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Fill input field
     * @param selector input selector
     * @param text text to fill
     * @return true if filled successfully
     */
    public boolean fillInput(String selector, String text) {
        try {
            getPage().fill(selector, text);
            logTestPass("✅ Filled input " + selector + " with: " + text);
            return true;
        } catch (Exception e) {
            logTestFail("❌ Failed to fill input " + selector + " - " + e.getMessage());
            return false;
        }
    }

    // ========================================
    // TEST METHODS (Moved from Runner Classes)
    // ========================================

    /**
     * Complete Reset Password Test Flow
     * Main test method that orchestrates the entire flow
     */
    @Test(description = "Reset Password Test - Complete Flow",
          groups = {"jeevansathi", "password-reset"}, priority = 1)
    public void resetPasswordTest() {
        logTestInfo("🔧 Starting complete reset password test flow");

        try {
            // Step 1: Navigate to homepage
            logTestInfo("=== STEP 1: NAVIGATION ===");
            navigateToHomepage();
            String pageTitle = getPageTitle();
            boolean pageLoaded = isPageLoaded();

            // Verify navigation using verification package
            verification.verifyHomepageNavigation(pageTitle);
            verification.verifyPageLoaded(pageLoaded);
            captureScreenshot("Homepage loaded");

            // Step 2: Click login button
            logTestInfo("=== STEP 2: LOGIN BUTTON CLICK ===");
            boolean loginClickResult = clickLoginButton();

            // Verify login button click using verification package
            verification.verifyLoginButtonClick(loginClickResult);
            captureScreenshot("After clicking login");

            // Step 3: Verify register button
            logTestInfo("=== STEP 3: REGISTER BUTTON VERIFICATION ===");
            boolean registerDisplayed = isRegisterButtonDisplayed();

            // Verify register button using verification package
            verification.verifyRegisterButtonDisplayed(registerDisplayed);
            captureScreenshot("Register button verification");

            // Step 4: Click forgot password
            logTestInfo("=== STEP 4: FORGOT PASSWORD CLICK ===");
            boolean forgotPasswordClickResult = clickForgotPasswordLink();

            // Verify forgot password click using verification package
            verification.verifyForgotPasswordClick(forgotPasswordClickResult);
            captureScreenshot("After clicking forgot password");

            // Step 5: Verify OTP button
            logTestInfo("=== STEP 5: GET OTP BUTTON VERIFICATION ===");
            boolean otpButtonAppearing = isGetOTPButtonAppearing();

            // Verify OTP button using verification package
            verification.verifyGetOTPButtonAppearing(otpButtonAppearing);
            captureScreenshot("Get OTP button verification");

            // Final verification using verification package
            logTestInfo("=== FINAL VERIFICATION ===");
            verification.verifyCompleteResetPasswordFlow(
                pageLoaded,
                loginClickResult,
                registerDisplayed,
                forgotPasswordClickResult,
                otpButtonAppearing
            );

            logTestPass("🎉 Reset Password Test completed successfully!");

        } catch (Exception e) {
            logTestFail("❌ Reset Password Test failed: " + e.getMessage());
            captureScreenshot("Test failure");
            throw e;
        }
    }

    /**
     * Simple Navigation Test
     * Demonstrates basic navigation and verification
     */
    @Test(description = "Simple Navigation Test",
          groups = {"jeevansathi", "navigation"}, priority = 2)
    public void simpleNavigationTest() {
        logTestInfo("🎯 Starting simple navigation test");

        try {
            // Perform navigation actions
            navigateToHomepage();
            String pageTitle = getPageTitle();
            boolean pageLoaded = isPageLoaded();
            captureScreenshot("Navigation completed");

            // Use verification package for assertions
            verification.verifyHomepageNavigation(pageTitle);
            verification.verifyPageLoaded(pageLoaded);

            logTestPass("🎉 Simple navigation test completed successfully!");

        } catch (Exception e) {
            logTestFail("❌ Simple navigation test failed: " + e.getMessage());
            captureScreenshot("Test failure");
            throw e;
        }
    }

    /**
     * Page Elements Verification Test
     * Checks for presence of key page elements
     */
    @Test(description = "Page Elements Verification",
          groups = {"jeevansathi", "verification"}, priority = 3)
    public void verifyPageElementsTest() {
        logTestInfo("🔍 Starting page elements verification test");

        try {
            // Navigate and verify basic loading
            navigateToHomepage();
            String pageTitle = getPageTitle();
            boolean pageLoaded = isPageLoaded();

            // Use verification package for basic checks
            verification.verifyHomepageNavigation(pageTitle);
            verification.verifyPageLoaded(pageLoaded);
            captureScreenshot("Homepage verification");

            // Check for common elements using logic methods
            boolean loginElementPresent = isElementPresent("text=/login/i");
            boolean registerElementPresent = isElementPresent("text=/register/i");
            boolean searchElementPresent = isElementPresent("input[type='search'], input[placeholder*='search']");

            // Use verification package for soft verification
            verification.softVerify(loginElementPresent,
                "Login element found on page",
                "Login element not found on page");

            verification.softVerify(registerElementPresent,
                "Registration element found on page",
                "Registration element not found on page");

            verification.softVerify(searchElementPresent,
                "Search functionality found on page",
                "Search functionality not found on page");

            captureScreenshot("Page elements verification completed");
            logTestPass("✅ Page elements verification test completed");

        } catch (Exception e) {
            logTestFail("❌ Page elements verification failed: " + e.getMessage());
            captureScreenshot("Verification failure");
            throw e;
        }
    }

    /**
     * Prompt-Based Test Generation
     * Users can modify the prompt to create different tests
     */
    @Test(description = "Prompt-Based Test Generation",
          groups = {"jeevansathi", "prompt-based"}, priority = 4)
    public void promptBasedTest() {

        // 🎯 USER: CHANGE THIS PROMPT TO CREATE YOUR TEST
        String userPrompt = "Navigate to Jeevansathi, click login, verify register button, click forgot password, check OTP button";
        String baseUrl = "https://www.jeevansathi.com/";

        logTestInfo("🤖 Executing prompt-based test");
        logTestInfo("📝 User Prompt: " + userPrompt);

        try {
            // Generate test steps from prompt
            List<TestCase> testCases = testGenerator.generateTestCases(userPrompt, baseUrl);

            if (testCases.isEmpty()) {
                logTestInfo("⚠️ No test cases generated, executing manual fallback");
                // Fallback to manual reset password flow
                executeManualResetPasswordFlow();
                return;
            }

            TestCase testCase = testCases.get(0);
            logTestInfo("✅ Generated test case: " + testCase.getName());
            logTestInfo("📊 Steps count: " + testCase.getSteps().size());

            // Execute generated test steps
            executeGeneratedTestSteps(testCase);

            logTestPass("🎉 Prompt-based test completed successfully!");

        } catch (Exception e) {
            logTestFail("❌ Prompt-based test failed: " + e.getMessage());
            captureScreenshot("Prompt test failure");
            throw e;
        }
    }

    /**
     * Augment Integration Demo
     * Shows how Augment integration works with fallbacks
     */
    @Test(description = "Augment Integration Demo",
          groups = {"jeevansathi", "augment"}, priority = 5)
    public void augmentIntegrationDemo() {

        logTestInfo("🤖 Demonstrating Augment Integration");

        // Check if Augment is enabled
        if (!augment.isAugmentEnabled()) {
            logTestInfo("⚠️ Augment integration not enabled in config");
            logTestInfo("💡 To enable: Set augment.enabled=true and augment.api.key in config.properties");
        } else {
            logTestInfo("✅ Augment integration is enabled");
        }

        // Test connection (async)
        augment.testConnection().thenAccept(isConnected -> {
            if (isConnected) {
                logTestPass("✅ Successfully connected to Augment API");
            } else {
                logTestInfo("⚠️ Augment API not available - using fallback mechanisms");
            }
        });

        // Demonstrate fallback test generation
        logTestInfo("🔄 Demonstrating intelligent fallback mechanisms");

        try {
            // Execute a simple test flow using fallback logic
            navigateToHomepage();
            boolean pageLoaded = isPageLoaded();
            verification.verifyPageLoaded(pageLoaded);

            boolean loginResult = clickLoginButton();
            verification.verifyLoginButtonClick(loginResult);

            captureScreenshot("Augment demo completed");
            logTestPass("🎉 Augment integration demo completed");

        } catch (Exception e) {
            logTestFail("❌ Augment demo failed: " + e.getMessage());
            captureScreenshot("Demo failure");
            throw e;
        }
    }

    // ========================================
    // HELPER METHODS FOR TEST EXECUTION
    // ========================================

    /**
     * Execute manual reset password flow (fallback)
     */
    private void executeManualResetPasswordFlow() {
        logTestInfo("🔧 Executing manual reset password flow as fallback");

        // Navigate
        navigateToHomepage();
        boolean pageLoaded = isPageLoaded();
        verification.verifyPageLoaded(pageLoaded);

        // Login
        boolean loginResult = clickLoginButton();
        verification.verifyLoginButtonClick(loginResult);

        // Register check
        boolean registerResult = isRegisterButtonDisplayed();
        verification.verifyRegisterButtonDisplayed(registerResult);

        // Forgot password
        boolean forgotResult = clickForgotPasswordLink();
        verification.verifyForgotPasswordClick(forgotResult);

        // OTP button
        boolean otpResult = isGetOTPButtonAppearing();
        verification.verifyGetOTPButtonAppearing(otpResult);

        captureScreenshot("Manual fallback test completed");
        logTestPass("✅ Manual reset password flow completed");
    }

    /**
     * Execute generated test steps from prompt
     */
    private void executeGeneratedTestSteps(TestCase testCase) {
        logTestInfo("🚀 Executing generated test steps for: " + testCase.getName());

        for (TestStep step : testCase.getSteps()) {
            logTestInfo("📋 Step " + step.getStepNumber() + ": " + step.getDescription());

            try {
                switch (step.getAction().toUpperCase()) {
                    case "NAVIGATE":
                        if (step.getInputData() != null) {
                            navigateTo(step.getInputData());
                            waitFor(2000);
                        }
                        break;

                    case "CLICK":
                        if (step.getLocator() != null) {
                            boolean clicked = clickElement(step.getLocator());
                            if (!clicked) {
                                // Try fallback click strategies
                                tryFallbackClick(step.getDescription());
                            }
                        }
                        break;

                    case "TYPE":
                    case "FILL":
                        if (step.getLocator() != null && step.getInputData() != null) {
                            fillInput(step.getLocator(), step.getInputData());
                        }
                        break;

                    case "VERIFY":
                    case "ASSERT":
                        if (step.getLocator() != null) {
                            boolean elementPresent = isElementPresent(step.getLocator());
                            verification.softVerify(elementPresent,
                                "Element verified: " + step.getDescription(),
                                "Element not found: " + step.getDescription());
                        }
                        break;

                    case "WAIT":
                        int waitTime = 2000;
                        if (step.getInputData() != null) {
                            try {
                                waitTime = Integer.parseInt(step.getInputData()) * 1000;
                            } catch (NumberFormatException ignored) {}
                        }
                        waitFor(waitTime);
                        break;

                    default:
                        logTestInfo("⚠️ Unknown action: " + step.getAction());
                        break;
                }

                logTestPass("✅ Step completed: " + step.getDescription());

            } catch (Exception e) {
                logTestInfo("⚠️ Step had issues: " + step.getDescription() + " - " + e.getMessage());
                // Continue with next step instead of failing entire test
            }
        }

        captureScreenshot("Generated test completed: " + testCase.getName());
    }

    /**
     * Try fallback click strategies based on description
     */
    private void tryFallbackClick(String description) {
        String desc = description.toLowerCase();

        if (desc.contains("login")) {
            clickLoginButton();
        } else if (desc.contains("register")) {
            // Try to click register button
            String[] registerSelectors = {"button:has-text('Register')", "a:has-text('Register')", "text=/register/i"};
            for (String selector : registerSelectors) {
                if (clickElement(selector)) {
                    logTestPass("✅ Fallback register click succeeded");
                    return;
                }
            }
        } else if (desc.contains("forgot") && desc.contains("password")) {
            clickForgotPasswordLink();
        } else {
            logTestInfo("⚠️ No specific fallback strategy for: " + description);
        }
    }
}
