package com.automation.logic;

import com.automation.core.TestBase;
import com.microsoft.playwright.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Jeevansathi Logic Class
 * Contains all business logic and actions for Jeevansathi.com
 * This class handles the "HOW" - the actual steps and interactions
 */
public class JeevansathiLogic extends TestBase {
    
    private static final Logger logger = LoggerFactory.getLogger(JeevansathiLogic.class);
    private final String BASE_URL = "https://www.jeevansathi.com/";
    
    /**
     * Navigate to Jeevansathi homepage
     */
    public void navigateToHomepage() {
        logTestInfo("🌐 Navigating to Jeevansathi homepage");
        navigateTo(BASE_URL);
        getPage().waitForTimeout(3000); // Wait for page to load
        logTestPass("✅ Successfully navigated to Jeevansathi homepage");
    }
    
    /**
     * Click on Login button
     * @return true if login button was clicked successfully
     */
    public boolean clickLoginButton() {
        logTestInfo("🔘 Attempting to click Login button");
        
        // Multiple selectors for login button
        String[] loginSelectors = {
            "a:has-text('Login')",
            "button:has-text('Login')", 
            ".login-btn",
            "#login-btn",
            "[data-testid='login']",
            "a[href*='login']",
            ".header-login",
            ".login-link",
            "[data-qa*='login']"
        };
        
        for (String selector : loginSelectors) {
            try {
                if (getPage().locator(selector).isVisible()) {
                    getPage().click(selector);
                    getPage().waitForTimeout(2000); // Wait for login modal/page to load
                    logTestPass("✅ Successfully clicked Login button using selector: " + selector);
                    return true;
                }
            } catch (Exception e) {
                logTestInfo("Login selector not found: " + selector);
            }
        }
        
        // Try generic text search as fallback
        try {
            getPage().locator("text=/login/i").first().click();
            getPage().waitForTimeout(2000);
            logTestPass("✅ Successfully clicked Login button using text search");
            return true;
        } catch (Exception e) {
            logTestFail("❌ Could not find or click Login button");
            return false;
        }
    }
    
    /**
     * Check if Register button is displayed
     * @return true if register button is found
     */
    public boolean isRegisterButtonDisplayed() {
        logTestInfo("🔍 Checking if Register button is displayed");
        
        String[] registerSelectors = {
            "#register_button",
            "button:has-text('Register for Free')",
            "a:has-text('Register')",
            "button:has-text('Register')",
            "a:has-text('Sign Up')",
            "button:has-text('Sign Up')",
            ".register-btn",
            "#register-btn",
            "[data-testid='register']",
            "a[href*='register']",
            ".signup-link",
            "[data-qa*='register']"
        };
        
        for (String selector : registerSelectors) {
            try {
                if (getPage().locator(selector).isVisible()) {
                    logTestPass("✅ Register button found using selector: " + selector);
                    return true;
                }
            } catch (Exception e) {
                logTestInfo("Register selector not found: " + selector);
            }
        }
        
        // Try to find the first register element (since we know there are multiple)
        try {
            if (getPage().locator("text=/register/i").first().isVisible()) {
                logTestPass("✅ Register element found using text search");
                return true;
            }
        } catch (Exception e) {
            logTestInfo("Register element not found with text search");
        }
        
        logTestFail("❌ Register button not found");
        return false;
    }
    
    /**
     * Click on Forgot Password link
     * @return true if forgot password link was clicked successfully
     */
    public boolean clickForgotPasswordLink() {
        logTestInfo("🔗 Attempting to click Forgot Password link");
        
        String[] forgotPasswordSelectors = {
            "a:has-text('Forgot Password')",
            "a:has-text('Forgot password')",
            "a:has-text('forgot password')",
            "button:has-text('Forgot Password')",
            ".forgot-password",
            "#forgot-password",
            "[data-testid='forgot-password']",
            "a[href*='forgot']",
            "a[href*='reset']",
            "[data-qa*='forgot']"
        };
        
        for (String selector : forgotPasswordSelectors) {
            try {
                if (getPage().locator(selector).isVisible()) {
                    getPage().click(selector);
                    getPage().waitForTimeout(3000); // Wait for forgot password page/section to load
                    logTestPass("✅ Successfully clicked Forgot Password using selector: " + selector);
                    return true;
                }
            } catch (Exception e) {
                logTestInfo("Forgot password selector not found: " + selector);
            }
        }
        
        // Try generic text search as fallback
        try {
            getPage().locator("text=/forgot.*password/i").first().click();
            getPage().waitForTimeout(3000);
            logTestPass("✅ Successfully clicked Forgot Password using text search");
            return true;
        } catch (Exception e) {
            logTestFail("❌ Could not find or click Forgot Password link");
            return false;
        }
    }
    
    /**
     * Check if Get OTP button is appearing
     * @return true if Get OTP button is found
     */
    public boolean isGetOTPButtonAppearing() {
        logTestInfo("🔍 Checking if Get OTP button is appearing");
        
        String[] otpButtonSelectors = {
            "button:has-text('Get OTP')",
            "button:has-text('Send OTP')",
            "button:has-text('Generate OTP')",
            "input[value*='OTP']",
            ".otp-btn",
            "#otp-btn",
            "#get-otp",
            "[data-testid='otp']",
            "button[type='submit']:has-text('OTP')",
            "[data-qa*='otp']"
        };
        
        for (String selector : otpButtonSelectors) {
            try {
                if (getPage().locator(selector).isVisible()) {
                    logTestPass("✅ Get OTP button found using selector: " + selector);
                    return true;
                }
            } catch (Exception e) {
                logTestInfo("OTP button selector not found: " + selector);
            }
        }
        
        // Try generic text search for OTP
        try {
            if (getPage().locator("text=/.*otp.*/i").first().isVisible()) {
                logTestPass("✅ OTP-related element found using text search");
                return true;
            }
        } catch (Exception e) {
            logTestInfo("OTP element not found with text search");
        }
        
        logTestFail("❌ Get OTP button not found");
        return false;
    }
    
    /**
     * Get page title
     * @return page title
     */
    public String getPageTitle() {
        String title = getPage().title();
        logTestInfo("📄 Page title: " + title);
        return title;
    }
    
    /**
     * Check if page is loaded properly
     * @return true if page is loaded
     */
    public boolean isPageLoaded() {
        try {
            getPage().waitForSelector("body", new Page.WaitForSelectorOptions().setTimeout(10000));
            String title = getPageTitle();
            boolean isLoaded = title.toLowerCase().contains("jeevansathi") || 
                             title.toLowerCase().contains("matrimony");
            
            if (isLoaded) {
                logTestPass("✅ Page loaded successfully");
            } else {
                logTestFail("❌ Page may not have loaded correctly");
            }
            
            return isLoaded;
        } catch (Exception e) {
            logTestFail("❌ Page loading failed: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Take screenshot with custom message
     * @param message screenshot message
     */
    public void captureScreenshot(String message) {
        takeScreenshot(message);
        logTestInfo("📸 Screenshot captured: " + message);
    }
    
    /**
     * Wait for specified time
     * @param milliseconds time to wait
     */
    public void waitFor(int milliseconds) {
        getPage().waitForTimeout(milliseconds);
        logTestInfo("⏳ Waited for " + milliseconds + "ms");
    }
    
    /**
     * Check if element exists on page
     * @param selector element selector
     * @return true if element exists
     */
    public boolean isElementPresent(String selector) {
        try {
            return getPage().locator(selector).count() > 0;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Get element text
     * @param selector element selector
     * @return element text or empty string if not found
     */
    public String getElementText(String selector) {
        try {
            return getPage().locator(selector).first().textContent();
        } catch (Exception e) {
            logTestInfo("Could not get text for selector: " + selector);
            return "";
        }
    }
    
    /**
     * Click element by selector
     * @param selector element selector
     * @return true if clicked successfully
     */
    public boolean clickElement(String selector) {
        try {
            getPage().click(selector);
            logTestPass("✅ Clicked element: " + selector);
            return true;
        } catch (Exception e) {
            logTestFail("❌ Failed to click element: " + selector + " - " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Fill input field
     * @param selector input selector
     * @param text text to fill
     * @return true if filled successfully
     */
    public boolean fillInput(String selector, String text) {
        try {
            getPage().fill(selector, text);
            logTestPass("✅ Filled input " + selector + " with: " + text);
            return true;
        } catch (Exception e) {
            logTestFail("❌ Failed to fill input " + selector + " - " + e.getMessage());
            return false;
        }
    }
}
