package com.automation.logic;

import com.automation.core.TestBase;
import com.automation.pages.HelpAndSupportPage;
import com.automation.verification.HelpAndSupportVerification;
import com.automation.utils.ActionUtility;
import org.testng.annotations.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * HelpAndSupport Logic Class - Business logic and test methods
 */
public class HelpAndSupportLogic extends TestBase {

    private static final Logger logger = LoggerFactory.getLogger(HelpAndSupportLogic.class);

    private final HelpAndSupportVerification verification = new HelpAndSupportVerification();
    private HelpAndSupportPage helpAndSupportPage;
    private ActionUtility actionUtility;

    private void initializePageObjects() {
        if (helpAndSupportPage == null) {
            helpAndSupportPage = new HelpAndSupportPage(getPage());
        }
        if (actionUtility == null) {
            actionUtility = new ActionUtility(getPage());
        }
    }

    @Test(description = "Help and Support Test",
          groups = {"help", "support", "functional"}, priority = 1)
    public void testHelpAndSupportFunctionality() {

        logTestInfo("🚀 Starting Help and Support test");

        try {
            initializePageObjects();

            // Navigate to Jeevansathi
            actionUtility.navigateTo("https://www.jeevansathi.com/");
            actionUtility.waitFor(3000);

            // Click on help button
            boolean helpClicked = helpAndSupportPage.clickHelpButton();
            verification.verifyHelpButtonClicked(helpClicked);

            // Verify if all categories getting displayed
            boolean categoriesDisplayed = helpAndSupportPage.verifyCategoriesDisplayed();
            verification.verifyCategoriesDisplayed(categoriesDisplayed);

            logTestPass("✅ Help and Support test completed successfully!");

        } catch (Exception e) {
            logTestFail("❌ Help and Support test failed: " + e.getMessage());
            actionUtility.takeScreenshot("help_support_test_failure");
            throw e;
        }
    }
}
