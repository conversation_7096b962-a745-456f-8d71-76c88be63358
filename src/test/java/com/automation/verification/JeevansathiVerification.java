package com.automation.verification;

import com.automation.core.TestBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.Assert;

/**
 * Jeevansathi Verification Class
 * Contains all verification and assertion logic for Jeevansathi.com
 * This class handles the "WHAT" - the expected results and validations
 */
public class JeevansathiVerification extends TestBase {
    
    private static final Logger logger = LoggerFactory.getLogger(JeevansathiVerification.class);
    
    /**
     * Verify that navigation to homepage was successful
     * @param actualTitle the actual page title
     */
    public void verifyHomepageNavigation(String actualTitle) {
        logTestInfo("🔍 Verifying homepage navigation");
        
        // Expected conditions for successful navigation
        boolean titleContainsJeevansathi = actualTitle.toLowerCase().contains("jeevansathi");
        boolean titleContainsMatrimony = actualTitle.toLowerCase().contains("matrimony");
        
        Assert.assertTrue(titleContainsJeevansathi || titleContainsMatrimony, 
            "Page title should contain 'Jeevansathi' or 'Matrimony'. Actual title: " + actualTitle);
        
        logTestPass("✅ Homepage navigation verified successfully");
        logTestPass("   Expected: Title containing 'Jeevansathi' or 'Matrimony'");
        logTestPass("   Actual: " + actualTitle);
    }
    
    /**
     * Verify that login button click was successful
     * @param clickResult the result of login button click
     */
    public void verifyLoginButtonClick(boolean clickResult) {
        logTestInfo("🔍 Verifying login button click");
        
        Assert.assertTrue(clickResult, "Login button should be clickable and click should be successful");
        
        logTestPass("✅ Login button click verified successfully");
        logTestPass("   Expected: Login button click should succeed");
        logTestPass("   Actual: Click was successful");
    }
    
    /**
     * Verify that register button is displayed
     * @param isDisplayed whether register button is displayed
     */
    public void verifyRegisterButtonDisplayed(boolean isDisplayed) {
        logTestInfo("🔍 Verifying register button is displayed");
        
        Assert.assertTrue(isDisplayed, "Register button should be displayed on the login page");
        
        logTestPass("✅ Register button display verified successfully");
        logTestPass("   Expected: Register button should be visible");
        logTestPass("   Actual: Register button is displayed");
    }
    
    /**
     * Verify that forgot password link click was successful
     * @param clickResult the result of forgot password link click
     */
    public void verifyForgotPasswordClick(boolean clickResult) {
        logTestInfo("🔍 Verifying forgot password link click");
        
        Assert.assertTrue(clickResult, "Forgot password link should be clickable and click should be successful");
        
        logTestPass("✅ Forgot password link click verified successfully");
        logTestPass("   Expected: Forgot password link click should succeed");
        logTestPass("   Actual: Click was successful");
    }
    
    /**
     * Verify that Get OTP button is appearing
     * @param isAppearing whether Get OTP button is appearing
     */
    public void verifyGetOTPButtonAppearing(boolean isAppearing) {
        logTestInfo("🔍 Verifying Get OTP button is appearing");
        
        Assert.assertTrue(isAppearing, "Get OTP button should be appearing on the forgot password page");
        
        logTestPass("✅ Get OTP button appearance verified successfully");
        logTestPass("   Expected: Get OTP button should be visible");
        logTestPass("   Actual: Get OTP button is appearing");
    }
    
    /**
     * Verify page loading
     * @param isLoaded whether page is loaded
     */
    public void verifyPageLoaded(boolean isLoaded) {
        logTestInfo("🔍 Verifying page is loaded");
        
        Assert.assertTrue(isLoaded, "Page should be loaded successfully");
        
        logTestPass("✅ Page loading verified successfully");
        logTestPass("   Expected: Page should load completely");
        logTestPass("   Actual: Page is loaded");
    }
    
    /**
     * Verify element presence
     * @param isPresent whether element is present
     * @param elementName name of the element for logging
     */
    public void verifyElementPresence(boolean isPresent, String elementName) {
        logTestInfo("🔍 Verifying " + elementName + " is present");
        
        Assert.assertTrue(isPresent, elementName + " should be present on the page");
        
        logTestPass("✅ " + elementName + " presence verified successfully");
        logTestPass("   Expected: " + elementName + " should be present");
        logTestPass("   Actual: " + elementName + " is present");
    }
    
    /**
     * Verify element text content
     * @param actualText actual text content
     * @param expectedText expected text content
     * @param elementName name of the element for logging
     */
    public void verifyElementText(String actualText, String expectedText, String elementName) {
        logTestInfo("🔍 Verifying " + elementName + " text content");
        
        Assert.assertEquals(actualText, expectedText, 
            elementName + " text should match expected value");
        
        logTestPass("✅ " + elementName + " text verified successfully");
        logTestPass("   Expected: " + expectedText);
        logTestPass("   Actual: " + actualText);
    }
    
    /**
     * Verify element text contains expected substring
     * @param actualText actual text content
     * @param expectedSubstring expected substring
     * @param elementName name of the element for logging
     */
    public void verifyElementTextContains(String actualText, String expectedSubstring, String elementName) {
        logTestInfo("🔍 Verifying " + elementName + " text contains: " + expectedSubstring);
        
        Assert.assertTrue(actualText.toLowerCase().contains(expectedSubstring.toLowerCase()), 
            elementName + " text should contain '" + expectedSubstring + "'. Actual text: " + actualText);
        
        logTestPass("✅ " + elementName + " text content verified successfully");
        logTestPass("   Expected to contain: " + expectedSubstring);
        logTestPass("   Actual text: " + actualText);
    }
    
    /**
     * Verify URL contains expected path
     * @param currentUrl current page URL
     * @param expectedPath expected URL path
     */
    public void verifyUrlContains(String currentUrl, String expectedPath) {
        logTestInfo("🔍 Verifying URL contains: " + expectedPath);
        
        Assert.assertTrue(currentUrl.toLowerCase().contains(expectedPath.toLowerCase()), 
            "URL should contain '" + expectedPath + "'. Actual URL: " + currentUrl);
        
        logTestPass("✅ URL verification successful");
        logTestPass("   Expected to contain: " + expectedPath);
        logTestPass("   Actual URL: " + currentUrl);
    }
    
    /**
     * Verify action result with custom message
     * @param result the result to verify
     * @param successMessage message for successful verification
     * @param failureMessage message for failed verification
     */
    public void verifyActionResult(boolean result, String successMessage, String failureMessage) {
        logTestInfo("🔍 Verifying action result");
        
        Assert.assertTrue(result, failureMessage);
        
        logTestPass("✅ " + successMessage);
    }
    
    /**
     * Verify multiple conditions (all must be true)
     * @param conditions array of boolean conditions
     * @param conditionNames array of condition names for logging
     */
    public void verifyAllConditions(boolean[] conditions, String[] conditionNames) {
        logTestInfo("🔍 Verifying multiple conditions");
        
        Assert.assertEquals(conditions.length, conditionNames.length, 
            "Conditions and condition names arrays must have same length");
        
        for (int i = 0; i < conditions.length; i++) {
            Assert.assertTrue(conditions[i], conditionNames[i] + " should be true");
            logTestPass("   ✅ " + conditionNames[i] + ": PASSED");
        }
        
        logTestPass("✅ All conditions verified successfully");
    }
    
    /**
     * Verify at least one condition is true
     * @param conditions array of boolean conditions
     * @param conditionNames array of condition names for logging
     */
    public void verifyAnyCondition(boolean[] conditions, String[] conditionNames) {
        logTestInfo("🔍 Verifying at least one condition is true");
        
        Assert.assertEquals(conditions.length, conditionNames.length, 
            "Conditions and condition names arrays must have same length");
        
        boolean anyTrue = false;
        StringBuilder resultLog = new StringBuilder();
        
        for (int i = 0; i < conditions.length; i++) {
            if (conditions[i]) {
                anyTrue = true;
                resultLog.append("   ✅ ").append(conditionNames[i]).append(": PASSED\n");
            } else {
                resultLog.append("   ❌ ").append(conditionNames[i]).append(": FAILED\n");
            }
        }
        
        Assert.assertTrue(anyTrue, "At least one condition should be true:\n" + resultLog.toString());
        
        logTestPass("✅ At least one condition verified successfully");
        logTestInfo(resultLog.toString());
    }
    
    /**
     * Soft verification - logs result but doesn't fail test
     * @param condition condition to verify
     * @param successMessage message for success
     * @param failureMessage message for failure
     * @return the verification result
     */
    public boolean softVerify(boolean condition, String successMessage, String failureMessage) {
        logTestInfo("🔍 Soft verification");
        
        if (condition) {
            logTestPass("✅ " + successMessage);
        } else {
            logTestInfo("⚠️ " + failureMessage + " (Soft verification - test continues)");
        }
        
        return condition;
    }
    
    /**
     * Verify complete reset password flow
     * @param navigationSuccess homepage navigation result
     * @param loginClickSuccess login button click result
     * @param registerDisplayed register button display result
     * @param forgotPasswordClickSuccess forgot password click result
     * @param otpButtonAppearing OTP button appearance result
     */
    public void verifyCompleteResetPasswordFlow(
            boolean navigationSuccess,
            boolean loginClickSuccess, 
            boolean registerDisplayed,
            boolean forgotPasswordClickSuccess,
            boolean otpButtonAppearing) {
        
        logTestInfo("🔍 Verifying complete reset password flow");
        
        // Verify each step
        verifyActionResult(navigationSuccess, 
            "Step 1: Homepage navigation successful", 
            "Step 1: Homepage navigation failed");
            
        verifyActionResult(loginClickSuccess, 
            "Step 2: Login button click successful", 
            "Step 2: Login button click failed");
            
        verifyActionResult(registerDisplayed, 
            "Step 3: Register button display verified", 
            "Step 3: Register button not displayed");
            
        verifyActionResult(forgotPasswordClickSuccess, 
            "Step 4: Forgot password click successful", 
            "Step 4: Forgot password click failed");
            
        verifyActionResult(otpButtonAppearing, 
            "Step 5: Get OTP button appearance verified", 
            "Step 5: Get OTP button not appearing");
        
        logTestPass("🎉 Complete reset password flow verified successfully!");
        logTestPass("   ✅ All 5 steps completed successfully");
        logTestPass("   ✅ Navigation → Login → Register Check → Forgot Password → OTP Button");
    }
}
