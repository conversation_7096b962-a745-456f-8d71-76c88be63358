package com.automation.verification;

import com.automation.core.TestBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * HelpAndSupport Verification Class - All verification logic
 */
public class HelpAndSupportVerification extends TestBase {

    private static final Logger logger = LoggerFactory.getLogger(HelpAndSupportVerification.class);

    public void verifyHelpButtonClicked(boolean clicked) {
        if (clicked) {
            logTestPass("✅ Help button clicked successfully");
        } else {
            logTestFail("❌ Help button click failed");
        }
    }

    public void verifyCategoriesDisplayed(boolean displayed) {
        if (displayed) {
            logTestPass("✅ All categories are displayed");
        } else {
            logTestFail("❌ Categories are not displayed properly");
        }
    }

    public void verifyElementPresent(boolean isPresent, String elementName) {
        if (isPresent) {
            logTestPass("✅ " + elementName + " is present");
        } else {
            logTestFail("❌ " + elementName + " is not present");
        }
    }
}
