package com.automation.tests;

import com.automation.core.TestBase;
import com.microsoft.playwright.assertions.PlaywrightAssertions;
import org.testng.annotations.Test;

/**
 * Manual Test Writing Examples
 * Write your own custom automation tests here
 */
public class MyManualTests extends TestBase {
    
    @Test(description = "Test Google Search Manually")
    public void testGoogleSearchManual() {
        logTestInfo("🔍 Starting Google search test");
        
        // Step 1: Navigate to Google
        navigateTo("https://google.com");
        logTestPass("Navigated to Google");
        
        // Step 2: Accept cookies if present
        try {
            if (getPage().locator("button:has-text('Accept'), button:has-text('I agree')").isVisible()) {
                getPage().click("button:has-text('Accept'), button:has-text('I agree')");
                logTestPass("Accepted cookies");
            }
        } catch (Exception e) {
            logTestInfo("No cookie banner found");
        }
        
        // Step 3: Search for something
        getPage().fill("input[name='q']", "Playwright automation testing");
        logTestPass("Entered search term");
        
        // Step 4: Submit search
        getPage().press("input[name='q']", "Enter");
        logTestPass("Submitted search");
        
        // Step 5: Wait for results
        getPage().waitForSelector("#search", new com.microsoft.playwright.Page.WaitForSelectorOptions().setTimeout(10000));
        
        // Step 6: Verify results
        PlaywrightAssertions.assertThat(getPage().locator("#search")).isVisible();
        logTestPass("Search results are visible");
        
        // Step 7: Take screenshot
        takeScreenshot("Google search results");
        
        logTestPass("✅ Google search test completed");
    }
    
    @Test(description = "Test Login Form")
    public void testLoginForm() {
        logTestInfo("🔐 Starting login form test");
        
        // Navigate to a demo login page
        navigateTo("https://the-internet.herokuapp.com/login");
        logTestPass("Navigated to login page");
        
        // Fill username
        getPage().fill("#username", "tomsmith");
        logTestPass("Entered username");
        
        // Fill password
        getPage().fill("#password", "SuperSecretPassword!");
        logTestPass("Entered password");
        
        // Click login button
        getPage().click("button[type='submit']");
        logTestPass("Clicked login button");
        
        // Verify successful login
        PlaywrightAssertions.assertThat(getPage().locator(".flash.success")).isVisible();
        logTestPass("Login successful message displayed");
        
        // Take screenshot
        takeScreenshot("Successful login");
        
        logTestPass("✅ Login test completed");
    }
    
    @Test(description = "Test E-commerce Shopping")
    public void testEcommerceShopping() {
        logTestInfo("🛒 Starting e-commerce shopping test");
        
        // Navigate to demo e-commerce site
        navigateTo("https://demo.opencart.com/");
        logTestPass("Navigated to OpenCart demo");
        
        // Search for a product
        getPage().fill("input[name='search']", "iPhone");
        getPage().click("button.btn-default");
        logTestPass("Searched for iPhone");
        
        // Wait for search results
        getPage().waitForSelector(".product-thumb", new com.microsoft.playwright.Page.WaitForSelectorOptions().setTimeout(10000));
        
        // Click on first product
        getPage().locator(".product-thumb").first().click();
        logTestPass("Clicked on first product");
        
        // Add to cart
        try {
            getPage().click("#button-cart");
            logTestPass("Added product to cart");
        } catch (Exception e) {
            logTestInfo("Add to cart button not found or not clickable");
        }
        
        // Take screenshot
        takeScreenshot("Product page");
        
        logTestPass("✅ E-commerce shopping test completed");
    }
    
    @Test(description = "Test Form Submission")
    public void testFormSubmission() {
        logTestInfo("📝 Starting form submission test");
        
        // Navigate to form demo
        navigateTo("https://formy-project.herokuapp.com/form");
        logTestPass("Navigated to form page");
        
        // Fill form fields
        getPage().fill("#first-name", "John");
        getPage().fill("#last-name", "Doe");
        getPage().fill("#job-title", "QA Engineer");
        logTestPass("Filled name and job title");
        
        // Select education level
        getPage().selectOption("#select-menu", "2");
        logTestPass("Selected education level");
        
        // Select gender
        getPage().check("#radio-button-2");
        logTestPass("Selected gender");
        
        // Select experience
        getPage().check("#checkbox-2");
        logTestPass("Selected experience");
        
        // Fill date
        getPage().fill("#datepicker", "01/01/2024");
        logTestPass("Filled date");
        
        // Submit form
        getPage().click(".btn-primary");
        logTestPass("Submitted form");
        
        // Verify success message
        try {
            getPage().waitForSelector(".alert-success", new com.microsoft.playwright.Page.WaitForSelectorOptions().setTimeout(10000));
            PlaywrightAssertions.assertThat(getPage().locator(".alert-success")).isVisible();
            logTestPass("Form submission successful");
        } catch (Exception e) {
            logTestInfo("Success message not found, but form was submitted");
        }
        
        // Take screenshot
        takeScreenshot("Form submission completed");
        
        logTestPass("✅ Form submission test completed");
    }
    
    @Test(description = "Test File Upload")
    public void testFileUpload() {
        logTestInfo("📁 Starting file upload test");
        
        // Navigate to file upload demo
        navigateTo("https://the-internet.herokuapp.com/upload");
        logTestPass("Navigated to file upload page");
        
        // Create a temporary test file
        try {
            java.nio.file.Path tempFile = java.nio.file.Files.createTempFile("test", ".txt");
            java.nio.file.Files.write(tempFile, "This is a test file for automation".getBytes());
            
            // Upload file
            getPage().setInputFiles("#file-upload", tempFile);
            logTestPass("Selected file for upload");
            
            // Click upload button
            getPage().click("#file-submit");
            logTestPass("Clicked upload button");
            
            // Verify upload success
            PlaywrightAssertions.assertThat(getPage().locator("#uploaded-files")).isVisible();
            logTestPass("File upload successful");
            
            // Clean up temp file
            java.nio.file.Files.deleteIfExists(tempFile);
            
        } catch (Exception e) {
            logTestInfo("File upload test had issues: " + e.getMessage());
        }
        
        // Take screenshot
        takeScreenshot("File upload completed");
        
        logTestPass("✅ File upload test completed");
    }
}
