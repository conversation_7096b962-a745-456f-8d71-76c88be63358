package com.automation.tests;

import com.automation.core.TestBase;
import com.automation.logic.JeevansathiLogic;
import com.automation.verification.JeevansathiVerification;
import com.automation.generator.TestGenerator;
import com.automation.models.TestCase;
import org.testng.annotations.Test;

import java.util.List;

/**
 * Jeevansathi.com Test Suite
 * Tests for https://www.jeevansathi.com/
 *
 * This class orchestrates the test execution by:
 * 1. Using JeevansathiLogic for business logic and actions
 * 2. Using JeevansathiVerification for assertions and validations
 * 3. Coordinating the test flow and reporting
 */
public class JeevansathiTests extends TestBase {

    private final TestGenerator testGenerator = new TestGenerator();
    private final JeevansathiLogic logic = new JeevansathiLogic();
    private final JeevansathiVerification verification = new JeevansathiVerification();
    
    @Test(description = "AI Generated: Reset Password Test for Jeevansathi", priority = 1, groups = {"jeevansathi", "password-reset"})
    public void resetPasswordTestAI() {
        logTestInfo("🤖 Starting AI-generated reset password test for Jeevansathi");

        // AI Prompt for your exact test case
        String prompt = "Navigate to Jeevansathi.com, click on login button, verify Register button is displayed, " +
                       "click on Forgot password, verify if Get OTP button is appearing";

        try {
            // Generate test cases from your prompt
            List<TestCase> testCases = testGenerator.generateTestCases(prompt, "https://www.jeevansathi.com/");

            if (!testCases.isEmpty()) {
                TestCase testCase = testCases.get(0);
                logTestInfo("✅ Generated test case: " + testCase.getName());
                executeGeneratedTestCase(testCase);
                logTestPass("🎉 AI-generated reset password test completed successfully!");
            } else {
                logTestInfo("⚠️ No test cases generated, executing manual fallback");
                resetPasswordTestManual(); // Fallback to manual test
            }

        } catch (Exception e) {
            logTestInfo("⚠️ AI generation failed, executing manual test: " + e.getMessage());
            resetPasswordTestManual(); // Fallback to manual test
        }
    }
    
    @Test(description = "Manual: Reset Password Test for Jeevansathi", priority = 2, groups = {"jeevansathi", "password-reset"})
    public void resetPasswordTestManual() {
        logTestInfo("🔧 Starting manual reset password test for Jeevansathi");

        try {
            // Step 1: Navigate to Jeevansathi.com
            logTestInfo("=== STEP 1: NAVIGATION ===");
            logic.navigateToHomepage();
            String pageTitle = logic.getPageTitle();
            boolean pageLoaded = logic.isPageLoaded();

            // Verify navigation
            verification.verifyHomepageNavigation(pageTitle);
            verification.verifyPageLoaded(pageLoaded);
            logic.captureScreenshot("Homepage loaded");

            // Step 2: Click on Login button
            logTestInfo("=== STEP 2: LOGIN BUTTON CLICK ===");
            boolean loginClickResult = logic.clickLoginButton();

            // Verify login button click
            verification.verifyLoginButtonClick(loginClickResult);
            logic.captureScreenshot("After clicking login");

            // Step 3: Verify if Register Button is displayed
            logTestInfo("=== STEP 3: REGISTER BUTTON VERIFICATION ===");
            boolean registerDisplayed = logic.isRegisterButtonDisplayed();

            // Verify register button display
            verification.verifyRegisterButtonDisplayed(registerDisplayed);
            logic.captureScreenshot("Register button verification");

            // Step 4: Click on Forgot Password
            logTestInfo("=== STEP 4: FORGOT PASSWORD CLICK ===");
            boolean forgotPasswordClickResult = logic.clickForgotPasswordLink();

            // Verify forgot password click
            verification.verifyForgotPasswordClick(forgotPasswordClickResult);
            logic.captureScreenshot("After clicking forgot password");

            // Step 5: Verify if Get OTP button is appearing
            logTestInfo("=== STEP 5: GET OTP BUTTON VERIFICATION ===");
            boolean otpButtonAppearing = logic.isGetOTPButtonAppearing();

            // Verify OTP button appearance
            verification.verifyGetOTPButtonAppearing(otpButtonAppearing);
            logic.captureScreenshot("Get OTP button verification");

            // Final verification of complete flow
            logTestInfo("=== FINAL VERIFICATION ===");
            verification.verifyCompleteResetPasswordFlow(
                pageLoaded,
                loginClickResult,
                registerDisplayed,
                forgotPasswordClickResult,
                otpButtonAppearing
            );

            logTestPass("🎉 Reset Password Test completed successfully!");

        } catch (Exception e) {
            logTestFail("❌ Reset Password Test failed: " + e.getMessage());
            logic.captureScreenshot("Test failure");
            throw e;
        }
    }
    
    @Test(description = "Jeevansathi Login Page Elements Verification", priority = 3, groups = {"jeevansathi", "verification"})
    public void verifyLoginPageElements() {
        logTestInfo("🔍 Starting login page elements verification");

        try {
            // Navigate to Jeevansathi
            logic.navigateToHomepage();
            String pageTitle = logic.getPageTitle();
            boolean pageLoaded = logic.isPageLoaded();

            // Verify basic page loading
            verification.verifyHomepageNavigation(pageTitle);
            verification.verifyPageLoaded(pageLoaded);
            logic.captureScreenshot("Jeevansathi homepage");

            // Check for common elements using logic
            logTestInfo("Checking for common page elements:");

            // Check for login-related elements
            boolean loginElementPresent = logic.isElementPresent("text=/login/i");
            verification.softVerify(loginElementPresent,
                "Login element found on page",
                "Login element not found on page");

            // Check for registration elements (use first() to avoid multiple elements issue)
            boolean registerElementPresent = logic.isElementPresent("text=/register/i");
            verification.softVerify(registerElementPresent,
                "Registration element found on page",
                "Registration element not found on page");

            // Check for search functionality
            boolean searchElementPresent = logic.isElementPresent("input[type='search'], input[placeholder*='search']");
            verification.softVerify(searchElementPresent,
                "Search functionality found on page",
                "Search functionality not found on page");

            logic.captureScreenshot("Page elements verification completed");
            logTestPass("✅ Login page elements verification completed");

        } catch (Exception e) {
            logTestFail("❌ Login page verification failed: " + e.getMessage());
            logic.captureScreenshot("Verification failure");
            throw e;
        }
    }
    
    // Helper method to execute generated test case
    private void executeGeneratedTestCase(TestCase testCase) {
        logTestInfo("📋 Executing generated test: " + testCase.getName());
        
        testCase.getSteps().forEach(step -> {
            logTestInfo("🔄 Step " + step.getStepNumber() + ": " + step.getDescription());
            
            try {
                switch (step.getAction()) {
                    case "NAVIGATE":
                        String url = step.getInputData() != null ? step.getInputData() : BASE_URL;
                        navigateTo(url);
                        break;
                    case "CLICK":
                        if (step.getLocator() != null) {
                            getPage().click(step.getLocator());
                        }
                        break;
                    case "TYPE":
                        if (step.getLocator() != null && step.getInputData() != null) {
                            getPage().fill(step.getLocator(), step.getInputData());
                        }
                        break;
                    case "VERIFY":
                        if (step.getExpectedResult() != null) {
                            getPage().waitForSelector("body", new com.microsoft.playwright.Page.WaitForSelectorOptions().setTimeout(5000));
                        }
                        break;
                    case "WAIT":
                        int waitTime = 2000;
                        if (step.getInputData() != null) {
                            try {
                                waitTime = Integer.parseInt(step.getInputData()) * 1000;
                            } catch (NumberFormatException ignored) {}
                        }
                        getPage().waitForTimeout(waitTime);
                        break;
                }
                
                logTestPass("✅ Step completed: " + step.getDescription());
                
            } catch (Exception e) {
                logTestInfo("⚠️ Step had issues: " + step.getDescription() + " - " + e.getMessage());
                // Continue with next step instead of failing entire test
            }
        });
        
        takeScreenshot("Generated test completed: " + testCase.getName());
    }
}
