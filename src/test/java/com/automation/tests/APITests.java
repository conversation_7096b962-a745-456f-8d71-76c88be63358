package com.automation.tests;

import com.automation.config.ConfigManager;
import com.automation.core.TestBase;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.classic.methods.HttpPut;
import org.apache.hc.client5.http.classic.methods.HttpDelete;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * API Test Suite
 * Contains tests for API endpoints and services
 */
public class APITests extends TestBase {
    
    private CloseableHttpClient httpClient;
    private ObjectMapper objectMapper;
    private ConfigManager config;
    private String apiBaseUrl;
    
    @BeforeClass
    public void setupAPITests() {
        httpClient = HttpClients.createDefault();
        objectMapper = new ObjectMapper();
        config = ConfigManager.getInstance();
        apiBaseUrl = config.getProperty("api.base.url", "https://jsonplaceholder.typicode.com");
        
        logTestInfo("API Tests setup completed");
        logTestInfo("API Base URL: " + apiBaseUrl);
    }
    
    @Test(description = "Test GET request for retrieving data", priority = 1, groups = {"api", "get"})
    public void testGetRequest() throws IOException {
        logTestInfo("Starting GET request test");
        
        String endpoint = apiBaseUrl + "/posts/1";
        HttpGet request = new HttpGet(endpoint);
        request.setHeader("Content-Type", "application/json");
        
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getCode();
            String responseBody = new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);
            
            logTestInfo("GET Request URL: " + endpoint);
            logTestInfo("Response Status: " + statusCode);
            logTestInfo("Response Body: " + responseBody);
            
            // Verify status code
            Assert.assertEquals(statusCode, 200, "Expected status code 200");
            logTestPass("Status code verification passed");
            
            // Verify response is valid JSON
            JsonNode jsonResponse = objectMapper.readTree(responseBody);
            Assert.assertNotNull(jsonResponse, "Response should be valid JSON");
            logTestPass("JSON response validation passed");
            
            // Verify response contains expected fields
            Assert.assertTrue(jsonResponse.has("id"), "Response should contain 'id' field");
            Assert.assertTrue(jsonResponse.has("title"), "Response should contain 'title' field");
            Assert.assertTrue(jsonResponse.has("body"), "Response should contain 'body' field");
            logTestPass("Response structure validation passed");
            
            // Verify specific values
            Assert.assertEquals(jsonResponse.get("id").asInt(), 1, "ID should be 1");
            logTestPass("ID value verification passed");
            
            takeScreenshot("GET request test completed");
            logTestPass("GET request test completed successfully");
        }
    }
    
    @Test(description = "Test POST request for creating data", priority = 2, groups = {"api", "post"})
    public void testPostRequest() throws IOException {
        logTestInfo("Starting POST request test");
        
        String endpoint = apiBaseUrl + "/posts";
        HttpPost request = new HttpPost(endpoint);
        request.setHeader("Content-Type", "application/json");
        
        // Create request body
        String requestBody = "{\n" +
                "    \"title\": \"Test Post\",\n" +
                "    \"body\": \"This is a test post created by automation\",\n" +
                "    \"userId\": 1\n" +
                "}";
        
        request.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));
        
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getCode();
            String responseBody = new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);
            
            logTestInfo("POST Request URL: " + endpoint);
            logTestInfo("Request Body: " + requestBody);
            logTestInfo("Response Status: " + statusCode);
            logTestInfo("Response Body: " + responseBody);
            
            // Verify status code (201 for created)
            Assert.assertEquals(statusCode, 201, "Expected status code 201 for created resource");
            logTestPass("Status code verification passed");
            
            // Verify response is valid JSON
            JsonNode jsonResponse = objectMapper.readTree(responseBody);
            Assert.assertNotNull(jsonResponse, "Response should be valid JSON");
            logTestPass("JSON response validation passed");
            
            // Verify response contains expected fields
            Assert.assertTrue(jsonResponse.has("id"), "Response should contain 'id' field");
            Assert.assertTrue(jsonResponse.has("title"), "Response should contain 'title' field");
            Assert.assertTrue(jsonResponse.has("body"), "Response should contain 'body' field");
            Assert.assertTrue(jsonResponse.has("userId"), "Response should contain 'userId' field");
            logTestPass("Response structure validation passed");
            
            // Verify created data
            Assert.assertEquals(jsonResponse.get("title").asText(), "Test Post", "Title should match");
            Assert.assertEquals(jsonResponse.get("userId").asInt(), 1, "User ID should be 1");
            logTestPass("Created data verification passed");
            
            takeScreenshot("POST request test completed");
            logTestPass("POST request test completed successfully");
        }
    }
    
    @Test(description = "Test PUT request for updating data", priority = 3, groups = {"api", "put"})
    public void testPutRequest() throws IOException {
        logTestInfo("Starting PUT request test");
        
        String endpoint = apiBaseUrl + "/posts/1";
        HttpPut request = new HttpPut(endpoint);
        request.setHeader("Content-Type", "application/json");
        
        // Create request body
        String requestBody = "{\n" +
                "    \"id\": 1,\n" +
                "    \"title\": \"Updated Test Post\",\n" +
                "    \"body\": \"This post has been updated by automation\",\n" +
                "    \"userId\": 1\n" +
                "}";
        
        request.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));
        
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getCode();
            String responseBody = new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);
            
            logTestInfo("PUT Request URL: " + endpoint);
            logTestInfo("Request Body: " + requestBody);
            logTestInfo("Response Status: " + statusCode);
            logTestInfo("Response Body: " + responseBody);
            
            // Verify status code
            Assert.assertEquals(statusCode, 200, "Expected status code 200");
            logTestPass("Status code verification passed");
            
            // Verify response is valid JSON
            JsonNode jsonResponse = objectMapper.readTree(responseBody);
            Assert.assertNotNull(jsonResponse, "Response should be valid JSON");
            logTestPass("JSON response validation passed");
            
            // Verify updated data
            Assert.assertEquals(jsonResponse.get("title").asText(), "Updated Test Post", "Title should be updated");
            Assert.assertEquals(jsonResponse.get("id").asInt(), 1, "ID should remain 1");
            logTestPass("Updated data verification passed");
            
            takeScreenshot("PUT request test completed");
            logTestPass("PUT request test completed successfully");
        }
    }
    
    @Test(description = "Test DELETE request for removing data", priority = 4, groups = {"api", "delete"})
    public void testDeleteRequest() throws IOException {
        logTestInfo("Starting DELETE request test");
        
        String endpoint = apiBaseUrl + "/posts/1";
        HttpDelete request = new HttpDelete(endpoint);
        request.setHeader("Content-Type", "application/json");
        
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getCode();
            
            logTestInfo("DELETE Request URL: " + endpoint);
            logTestInfo("Response Status: " + statusCode);
            
            // Verify status code
            Assert.assertEquals(statusCode, 200, "Expected status code 200");
            logTestPass("Status code verification passed");
            
            takeScreenshot("DELETE request test completed");
            logTestPass("DELETE request test completed successfully");
        }
    }
    
    @Test(description = "Test API error handling", priority = 5, groups = {"api", "error"})
    public void testAPIErrorHandling() throws IOException {
        logTestInfo("Starting API error handling test");
        
        // Test 404 error
        String endpoint = apiBaseUrl + "/posts/999999";
        HttpGet request = new HttpGet(endpoint);
        request.setHeader("Content-Type", "application/json");
        
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getCode();
            
            logTestInfo("GET Request URL (non-existent): " + endpoint);
            logTestInfo("Response Status: " + statusCode);
            
            // Verify 404 status code
            Assert.assertEquals(statusCode, 404, "Expected status code 404 for non-existent resource");
            logTestPass("404 error handling verification passed");
        }
        
        takeScreenshot("API error handling test completed");
        logTestPass("API error handling test completed successfully");
    }
    
    @Test(description = "Test API response time", priority = 6, groups = {"api", "performance"})
    public void testAPIResponseTime() throws IOException {
        logTestInfo("Starting API response time test");
        
        String endpoint = apiBaseUrl + "/posts";
        HttpGet request = new HttpGet(endpoint);
        request.setHeader("Content-Type", "application/json");
        
        long startTime = System.currentTimeMillis();
        
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            long endTime = System.currentTimeMillis();
            long responseTime = endTime - startTime;
            
            int statusCode = response.getCode();
            
            logTestInfo("GET Request URL: " + endpoint);
            logTestInfo("Response Status: " + statusCode);
            logTestInfo("Response Time: " + responseTime + " ms");
            
            // Verify status code
            Assert.assertEquals(statusCode, 200, "Expected status code 200");
            logTestPass("Status code verification passed");
            
            // Verify response time (should be less than 5 seconds)
            Assert.assertTrue(responseTime < 5000, "Response time should be less than 5 seconds. Actual: " + responseTime + " ms");
            logTestPass("Response time verification passed: " + responseTime + " ms");
            
            takeScreenshot("API response time test completed");
            logTestPass("API response time test completed successfully");
        }
    }
    
    @Test(description = "Test API data validation", priority = 7, groups = {"api", "validation"})
    public void testAPIDataValidation() throws IOException {
        logTestInfo("Starting API data validation test");
        
        String endpoint = apiBaseUrl + "/users";
        HttpGet request = new HttpGet(endpoint);
        request.setHeader("Content-Type", "application/json");
        
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getCode();
            String responseBody = new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);
            
            logTestInfo("GET Request URL: " + endpoint);
            logTestInfo("Response Status: " + statusCode);
            
            // Verify status code
            Assert.assertEquals(statusCode, 200, "Expected status code 200");
            logTestPass("Status code verification passed");
            
            // Verify response is valid JSON array
            JsonNode jsonResponse = objectMapper.readTree(responseBody);
            Assert.assertTrue(jsonResponse.isArray(), "Response should be a JSON array");
            logTestPass("JSON array validation passed");
            
            // Verify array is not empty
            Assert.assertTrue(jsonResponse.size() > 0, "Response array should not be empty");
            logTestPass("Non-empty array validation passed");
            
            // Validate first user object structure
            JsonNode firstUser = jsonResponse.get(0);
            String[] requiredFields = {"id", "name", "username", "email"};
            
            for (String field : requiredFields) {
                Assert.assertTrue(firstUser.has(field), "User object should contain '" + field + "' field");
            }
            logTestPass("User object structure validation passed");
            
            // Validate email format
            String email = firstUser.get("email").asText();
            Assert.assertTrue(email.contains("@"), "Email should contain @ symbol");
            logTestPass("Email format validation passed");
            
            takeScreenshot("API data validation test completed");
            logTestPass("API data validation test completed successfully");
        }
    }
}
