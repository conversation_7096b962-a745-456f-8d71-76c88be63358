package com.automation.tests;

import com.automation.core.TestBase;
import com.automation.generator.TestGenerator;
import com.automation.models.TestCase;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * Tests generated from external prompt file
 * Read prompts from test-prompts.txt and convert to automated tests
 */
public class PromptBasedTests extends TestBase {
    
    private final TestGenerator testGenerator = new TestGenerator();
    
    @DataProvider(name = "promptsFromFile")
    public Object[][] getPromptsFromFile() {
        List<Object[]> prompts = new ArrayList<>();
        
        try {
            List<String> lines = Files.readAllLines(Paths.get("test-prompts.txt"));
            
            for (String line : lines) {
                line = line.trim();
                // Skip comments and empty lines
                if (!line.isEmpty() && !line.startsWith("#")) {
                    // Extract base URL from prompt or use default
                    String baseUrl = extractBaseUrl(line);
                    prompts.add(new Object[]{line, baseUrl});
                }
            }
            
        } catch (IOException e) {
            logTestInfo("Could not read test-prompts.txt file: " + e.getMessage());
            // Add default prompts if file not found
            prompts.add(new Object[]{"Navigate to Google and search for Playwright automation", "https://google.com"});
        }
        
        return prompts.toArray(new Object[0][]);
    }
    
    @Test(dataProvider = "promptsFromFile", description = "Execute tests from prompt file")
    public void testFromPromptFile(String prompt, String baseUrl) {
        logTestInfo("🤖 Executing prompt: " + prompt);
        
        try {
            List<TestCase> testCases = testGenerator.generateTestCases(prompt, baseUrl);
            
            if (!testCases.isEmpty()) {
                TestCase testCase = testCases.get(0);
                executeGeneratedTestCase(testCase);
                logTestPass("✅ Prompt-based test completed: " + prompt);
            } else {
                logTestInfo("⚠️ No test cases generated for prompt: " + prompt);
            }
            
        } catch (Exception e) {
            logTestFail("❌ Prompt execution failed: " + e.getMessage());
            // Don't throw exception to continue with other prompts
        }
    }
    
    private String extractBaseUrl(String prompt) {
        String lowerPrompt = prompt.toLowerCase();
        
        // Extract common websites from prompts
        if (lowerPrompt.contains("amazon")) return "https://amazon.com";
        if (lowerPrompt.contains("google")) return "https://google.com";
        if (lowerPrompt.contains("facebook")) return "https://facebook.com";
        if (lowerPrompt.contains("twitter")) return "https://twitter.com";
        if (lowerPrompt.contains("linkedin")) return "https://linkedin.com";
        if (lowerPrompt.contains("netflix")) return "https://netflix.com";
        if (lowerPrompt.contains("youtube")) return "https://youtube.com";
        if (lowerPrompt.contains("gmail")) return "https://gmail.com";
        if (lowerPrompt.contains("expedia")) return "https://expedia.com";
        if (lowerPrompt.contains("uber")) return "https://ubereats.com";
        if (lowerPrompt.contains("coursera")) return "https://coursera.org";
        if (lowerPrompt.contains("bbc")) return "https://bbc.com";
        if (lowerPrompt.contains("zillow")) return "https://zillow.com";
        if (lowerPrompt.contains("target")) return "https://target.com";
        if (lowerPrompt.contains("coinbase")) return "https://coinbase.com";
        if (lowerPrompt.contains("drive")) return "https://drive.google.com";
        
        // Default URL
        return "https://example.com";
    }
    
    private void executeGeneratedTestCase(TestCase testCase) {
        logTestInfo("📋 Executing: " + testCase.getName());
        
        testCase.getSteps().forEach(step -> {
            logTestInfo("🔄 Step " + step.getStepNumber() + ": " + step.getDescription());
            
            try {
                switch (step.getAction()) {
                    case "NAVIGATE":
                        String url = step.getInputData() != null ? step.getInputData() : testCase.getUrl();
                        navigateTo(url);
                        break;
                    case "CLICK":
                        if (step.getLocator() != null) {
                            getPage().click(step.getLocator());
                        }
                        break;
                    case "TYPE":
                        if (step.getLocator() != null && step.getInputData() != null) {
                            getPage().fill(step.getLocator(), step.getInputData());
                        }
                        break;
                    case "SELECT":
                        if (step.getLocator() != null && step.getInputData() != null) {
                            getPage().selectOption(step.getLocator(), step.getInputData());
                        }
                        break;
                    case "VERIFY":
                        if (step.getExpectedResult() != null) {
                            getPage().waitForSelector("body", new com.microsoft.playwright.Page.WaitForSelectorOptions().setTimeout(5000));
                        }
                        break;
                    case "WAIT":
                        int waitTime = 2000;
                        if (step.getInputData() != null) {
                            try {
                                waitTime = Integer.parseInt(step.getInputData()) * 1000;
                            } catch (NumberFormatException ignored) {}
                        }
                        getPage().waitForTimeout(waitTime);
                        break;
                }
                
                logTestPass("✅ Step completed: " + step.getDescription());
                
            } catch (Exception e) {
                logTestInfo("⚠️ Step had issues: " + step.getDescription() + " - " + e.getMessage());
                // Continue with next step instead of failing entire test
            }
        });
        
        takeScreenshot("Prompt test completed: " + testCase.getName());
    }
}
