package com.automation.tests;

import com.automation.core.TestBase;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.assertions.PlaywrightAssertions;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * Smoke Test Suite
 * Contains basic smoke tests to verify core functionality
 */
public class SmokeTests extends TestBase {
    
    @Test(description = "Verify application loads successfully", priority = 1, groups = {"smoke"})
    public void testApplicationLoads() {
        logTestInfo("Starting application load test");
        
        // Navigate to base URL
        String baseUrl = System.getProperty("automation.base.url", "https://example.com");
        navigateTo(baseUrl);
        
        // Verify page title is not empty
        String title = getPage().title();
        Assert.assertFalse(title.isEmpty(), "Page title should not be empty");
        logTestPass("Page title verified: " + title);
        
        // Verify page is loaded (body element exists)
        PlaywrightAssertions.assertThat(getPage().locator("body")).isVisible();
        logTestPass("Page body is visible");
        
        // Take screenshot
        takeScreenshot("Application loaded successfully");
        
        logTestPass("Application loads test completed successfully");
    }
    
    @Test(description = "Verify page navigation works", priority = 2, groups = {"smoke"})
    public void testPageNavigation() {
        logTestInfo("Starting page navigation test");
        
        Page page = getPage();
        String baseUrl = System.getProperty("automation.base.url", "https://example.com");
        
        // Navigate to base URL
        navigateTo(baseUrl);
        
        // Verify current URL
        String currentUrl = page.url();
        Assert.assertTrue(currentUrl.contains(baseUrl), 
            "Current URL should contain base URL. Expected: " + baseUrl + ", Actual: " + currentUrl);
        logTestPass("Navigation to base URL successful");
        
        // Check if there are any navigation links
        if (page.locator("a").count() > 0) {
            logTestInfo("Found " + page.locator("a").count() + " navigation links");
            
            // Get first link and verify it's clickable
            String firstLinkText = page.locator("a").first().textContent();
            if (firstLinkText != null && !firstLinkText.trim().isEmpty()) {
                logTestInfo("First link text: " + firstLinkText);
                PlaywrightAssertions.assertThat(page.locator("a").first()).isVisible();
                logTestPass("Navigation links are visible and accessible");
            }
        }
        
        takeScreenshot("Page navigation verified");
        logTestPass("Page navigation test completed successfully");
    }
    
    @Test(description = "Verify basic page elements are present", priority = 3, groups = {"smoke"})
    public void testBasicPageElements() {
        logTestInfo("Starting basic page elements test");
        
        Page page = getPage();
        String baseUrl = System.getProperty("automation.base.url", "https://example.com");
        navigateTo(baseUrl);
        
        // Check for common page elements
        boolean hasHeader = page.locator("header, .header, #header").count() > 0;
        boolean hasNav = page.locator("nav, .nav, .navigation").count() > 0;
        boolean hasMain = page.locator("main, .main, #main").count() > 0;
        boolean hasFooter = page.locator("footer, .footer, #footer").count() > 0;
        
        logTestInfo("Page structure analysis:");
        logTestInfo("- Header present: " + hasHeader);
        logTestInfo("- Navigation present: " + hasNav);
        logTestInfo("- Main content present: " + hasMain);
        logTestInfo("- Footer present: " + hasFooter);
        
        // At least body should be present
        PlaywrightAssertions.assertThat(page.locator("body")).isVisible();
        logTestPass("Body element is present and visible");
        
        // Check for any form elements
        int formCount = (int) page.locator("form").count();
        int inputCount = (int) page.locator("input").count();
        int buttonCount = (int) page.locator("button").count();
        
        logTestInfo("Interactive elements found:");
        logTestInfo("- Forms: " + formCount);
        logTestInfo("- Input fields: " + inputCount);
        logTestInfo("- Buttons: " + buttonCount);
        
        takeScreenshot("Basic page elements verified");
        logTestPass("Basic page elements test completed successfully");
    }
    
    @Test(description = "Verify page responsiveness", priority = 4, groups = {"smoke"})
    public void testPageResponsiveness() {
        logTestInfo("Starting page responsiveness test");
        
        Page page = getPage();
        String baseUrl = System.getProperty("automation.base.url", "https://example.com");
        navigateTo(baseUrl);
        
        // Test different viewport sizes
        int[][] viewports = {
            {1920, 1080}, // Desktop
            {1366, 768},  // Laptop
            {768, 1024},  // Tablet
            {375, 667}    // Mobile
        };
        
        for (int[] viewport : viewports) {
            int width = viewport[0];
            int height = viewport[1];
            
            logTestInfo("Testing viewport: " + width + "x" + height);
            
            // Set viewport size
            page.setViewportSize(width, height);
            
            // Wait for any responsive changes
            page.waitForTimeout(1000);
            
            // Verify page is still functional
            PlaywrightAssertions.assertThat(page.locator("body")).isVisible();
            
            // Take screenshot for this viewport
            takeScreenshot("Responsive_" + width + "x" + height);
            
            logTestPass("Viewport " + width + "x" + height + " test passed");
        }
        
        // Reset to default viewport
        page.setViewportSize(1920, 1080);
        
        logTestPass("Page responsiveness test completed successfully");
    }
    
    @Test(description = "Verify page performance basics", priority = 5, groups = {"smoke"})
    public void testPagePerformance() {
        logTestInfo("Starting page performance test");
        
        Page page = getPage();
        String baseUrl = System.getProperty("automation.base.url", "https://example.com");
        
        // Measure page load time
        long startTime = System.currentTimeMillis();
        navigateTo(baseUrl);
        
        // Wait for page to be fully loaded
        page.waitForLoadState(com.microsoft.playwright.Page.LoadState.NETWORKIDLE);
        long loadTime = System.currentTimeMillis() - startTime;
        
        logTestInfo("Page load time: " + loadTime + " ms");
        
        // Basic performance assertions
        Assert.assertTrue(loadTime < 10000, "Page should load within 10 seconds. Actual: " + loadTime + " ms");
        logTestPass("Page load time is acceptable: " + loadTime + " ms");
        
        // Verify page title loads
        String title = page.title();
        Assert.assertNotNull(title, "Page title should not be null");
        logTestPass("Page title loaded: " + title);
        
        takeScreenshot("Page performance verified");
        logTestPass("Page performance test completed successfully");
    }
}
