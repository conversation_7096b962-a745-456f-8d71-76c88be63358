package com.automation.tests;

import com.automation.core.TestBase;
import com.microsoft.playwright.assertions.PlaywrightAssertions;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * Regression Test Suite
 * Contains comprehensive tests for regression testing
 */
public class RegressionTests extends TestBase {
    
    @Test(description = "Verify complete user registration flow", priority = 1, groups = {"regression"})
    public void testUserRegistrationFlow() {
        logTestInfo("Starting user registration flow test");
        
        String baseUrl = System.getProperty("automation.base.url", "https://example.com");
        navigateTo(baseUrl);
        
        // Verify page loads
        PlaywrightAssertions.assertThat(getPage().locator("body")).isVisible();
        logTestPass("Page loaded successfully");
        
        // Look for registration elements (example selectors)
        try {
            if (getPage().locator("a:has-text('Sign Up')").isVisible()) {
                getPage().click("a:has-text('Sign Up')");
                logTestPass("Clicked Sign Up link");
                
                // Fill registration form if it exists
                if (getPage().locator("input[type='email']").isVisible()) {
                    getPage().fill("input[type='email']", "<EMAIL>");
                    logTestPass("Entered email");
                }
                
                if (getPage().locator("input[type='password']").isVisible()) {
                    getPage().fill("input[type='password']", "TestPassword123");
                    logTestPass("Entered password");
                }
                
                // Submit form if submit button exists
                if (getPage().locator("button:has-text('Register'), input[type='submit']").isVisible()) {
                    getPage().click("button:has-text('Register'), input[type='submit']");
                    logTestPass("Clicked register button");
                }
            } else {
                logTestInfo("Registration form not found on this page");
            }
        } catch (Exception e) {
            logTestInfo("Registration flow elements not available: " + e.getMessage());
        }
        
        takeScreenshot("User registration flow completed");
        logTestPass("User registration flow test completed");
    }
    
    @Test(description = "Verify user login functionality", priority = 2, groups = {"regression"})
    public void testUserLogin() {
        logTestInfo("Starting user login test");
        
        String baseUrl = System.getProperty("automation.base.url", "https://example.com");
        navigateTo(baseUrl);
        
        try {
            // Look for login elements
            if (getPage().locator("a:has-text('Login'), a:has-text('Sign In')").isVisible()) {
                getPage().click("a:has-text('Login'), a:has-text('Sign In')");
                logTestPass("Clicked login link");
                
                // Fill login form
                if (getPage().locator("input[type='email'], input[name*='email'], input[name*='username']").isVisible()) {
                    getPage().fill("input[type='email'], input[name*='email'], input[name*='username']", "<EMAIL>");
                    logTestPass("Entered username/email");
                }
                
                if (getPage().locator("input[type='password']").isVisible()) {
                    getPage().fill("input[type='password']", "TestPassword123");
                    logTestPass("Entered password");
                }
                
                // Submit login
                if (getPage().locator("button:has-text('Login'), button:has-text('Sign In'), input[type='submit']").isVisible()) {
                    getPage().click("button:has-text('Login'), button:has-text('Sign In'), input[type='submit']");
                    logTestPass("Clicked login button");
                }
            } else {
                logTestInfo("Login form not found on this page");
            }
        } catch (Exception e) {
            logTestInfo("Login elements not available: " + e.getMessage());
        }
        
        takeScreenshot("User login completed");
        logTestPass("User login test completed");
    }
    
    @Test(description = "Verify search functionality", priority = 3, groups = {"regression"})
    public void testSearchFunctionality() {
        logTestInfo("Starting search functionality test");
        
        String baseUrl = System.getProperty("automation.base.url", "https://example.com");
        navigateTo(baseUrl);
        
        try {
            // Look for search elements
            if (getPage().locator("input[type='search'], input[name*='search'], input[placeholder*='search']").isVisible()) {
                String searchTerm = "test search";
                getPage().fill("input[type='search'], input[name*='search'], input[placeholder*='search']", searchTerm);
                logTestPass("Entered search term: " + searchTerm);
                
                // Submit search
                if (getPage().locator("button:has-text('Search'), input[type='submit']").isVisible()) {
                    getPage().click("button:has-text('Search'), input[type='submit']");
                    logTestPass("Clicked search button");
                } else {
                    // Try pressing Enter
                    getPage().locator("input[type='search'], input[name*='search'], input[placeholder*='search']").press("Enter");
                    logTestPass("Pressed Enter to search");
                }
                
                // Wait for results
                getPage().waitForTimeout(2000);
                
                // Verify search results or no results message
                if (getPage().locator("text=No results, text=No matches, text=0 results").isVisible()) {
                    logTestPass("No results message displayed");
                } else {
                    logTestPass("Search completed (results may be present)");
                }
            } else {
                logTestInfo("Search functionality not found on this page");
            }
        } catch (Exception e) {
            logTestInfo("Search elements not available: " + e.getMessage());
        }
        
        takeScreenshot("Search functionality completed");
        logTestPass("Search functionality test completed");
    }
    
    @Test(description = "Verify navigation menu functionality", priority = 4, groups = {"regression"})
    public void testNavigationMenu() {
        logTestInfo("Starting navigation menu test");
        
        String baseUrl = System.getProperty("automation.base.url", "https://example.com");
        navigateTo(baseUrl);
        
        try {
            // Look for navigation elements
            if (getPage().locator("nav, .navbar, .menu, .navigation").isVisible()) {
                logTestPass("Navigation menu found");
                
                // Test common navigation links
                String[] commonLinks = {"Home", "About", "Contact", "Services", "Products"};
                
                for (String linkText : commonLinks) {
                    if (getPage().locator("a:has-text('" + linkText + "')").isVisible()) {
                        getPage().click("a:has-text('" + linkText + "')");
                        logTestPass("Clicked " + linkText + " link");
                        
                        // Wait for page to load
                        getPage().waitForTimeout(1000);
                        
                        // Verify page loaded
                        PlaywrightAssertions.assertThat(getPage().locator("body")).isVisible();
                        logTestPass(linkText + " page loaded successfully");
                        
                        // Go back to home
                        navigateTo(baseUrl);
                        getPage().waitForTimeout(1000);
                    }
                }
            } else {
                logTestInfo("Navigation menu not found on this page");
            }
        } catch (Exception e) {
            logTestInfo("Navigation elements not available: " + e.getMessage());
        }
        
        takeScreenshot("Navigation menu test completed");
        logTestPass("Navigation menu test completed");
    }
    
    @Test(description = "Verify form validation", priority = 5, groups = {"regression"})
    public void testFormValidation() {
        logTestInfo("Starting form validation test");
        
        String baseUrl = System.getProperty("automation.base.url", "https://example.com");
        navigateTo(baseUrl);
        
        try {
            // Look for forms
            if (getPage().locator("form").isVisible()) {
                logTestPass("Form found on page");
                
                // Try to submit empty form
                if (getPage().locator("input[type='submit'], button[type='submit']").isVisible()) {
                    getPage().click("input[type='submit'], button[type='submit']");
                    logTestPass("Attempted to submit empty form");
                    
                    // Wait for validation messages
                    getPage().waitForTimeout(1000);
                    
                    // Check for validation messages
                    if (getPage().locator("text=required, text=invalid, text=error, .error, .invalid").isVisible()) {
                        logTestPass("Validation messages displayed");
                    } else {
                        logTestInfo("No validation messages found");
                    }
                }
                
                // Test with invalid email if email field exists
                if (getPage().locator("input[type='email']").isVisible()) {
                    getPage().fill("input[type='email']", "invalid-email");
                    logTestPass("Entered invalid email");
                    
                    if (getPage().locator("input[type='submit'], button[type='submit']").isVisible()) {
                        getPage().click("input[type='submit'], button[type='submit']");
                        getPage().waitForTimeout(1000);
                        
                        if (getPage().locator("text=invalid email, text=valid email, .error").isVisible()) {
                            logTestPass("Email validation working");
                        }
                    }
                }
            } else {
                logTestInfo("No forms found on this page");
            }
        } catch (Exception e) {
            logTestInfo("Form validation elements not available: " + e.getMessage());
        }
        
        takeScreenshot("Form validation test completed");
        logTestPass("Form validation test completed");
    }
    
    @Test(description = "Verify responsive design", priority = 6, groups = {"regression"})
    public void testResponsiveDesign() {
        logTestInfo("Starting responsive design test");
        
        String baseUrl = System.getProperty("automation.base.url", "https://example.com");
        navigateTo(baseUrl);
        
        // Test different viewport sizes
        int[][] viewports = {
            {1920, 1080}, // Desktop
            {1366, 768},  // Laptop
            {768, 1024},  // Tablet
            {375, 667}    // Mobile
        };
        
        for (int[] viewport : viewports) {
            int width = viewport[0];
            int height = viewport[1];
            
            getPage().setViewportSize(width, height);
            logTestPass("Set viewport to " + width + "x" + height);
            
            // Wait for layout to adjust
            getPage().waitForTimeout(1000);
            
            // Verify page is still functional
            PlaywrightAssertions.assertThat(getPage().locator("body")).isVisible();
            logTestPass("Page is visible at " + width + "x" + height);
            
            // Take screenshot for each viewport
            takeScreenshot("Responsive_" + width + "x" + height);
        }
        
        logTestPass("Responsive design test completed");
    }
}
