package com.automation.tests;

import com.automation.core.TestBase;
import com.automation.generator.TestGenerator;
import com.automation.models.TestCase;
import org.testng.annotations.Test;

import java.util.List;

/**
 * Custom Tests - Add your automation prompts here
 */
public class MyCustomTests extends TestBase {
    
    private final TestGenerator testGenerator = new TestGenerator();
    
    @Test(description = "AI Generated: Login to Gmail and check inbox")
    public void testGmailLogin() {
        // 🎯 YOUR PROMPT HERE - Just write what you want to test in plain English!
        String prompt = "Navigate to Gmail, login with username '<EMAIL>' and password 'mypassword', then verify inbox is loaded";
        String baseUrl = "https://gmail.com";
        
        // The framework will convert your prompt to automated test steps
        executeAIGeneratedTest(prompt, baseUrl);
    }
    
    @Test(description = "AI Generated: Amazon product search")
    public void testAmazonSearch() {
        // 🎯 ANOTHER PROMPT EXAMPLE
        String prompt = "Go to Amazon, search for 'iPhone 15', filter by 4+ stars rating, and verify search results are displayed";
        String baseUrl = "https://amazon.com";
        
        executeAIGeneratedTest(prompt, baseUrl);
    }
    
    @Test(description = "AI Generated: Facebook post creation")
    public void testFacebookPost() {
        // 🎯 SOCIAL MEDIA AUTOMATION PROMPT
        String prompt = "Login to Facebook, create a new post with text 'Hello from automation!', add a photo, and publish the post";
        String baseUrl = "https://facebook.com";
        
        executeAIGeneratedTest(prompt, baseUrl);
    }
    
    @Test(description = "AI Generated: Online banking transaction")
    public void testBankingTransaction() {
        // 🎯 BANKING AUTOMATION PROMPT
        String prompt = "Login to online banking, navigate to transfer money, enter amount $100, select recipient account, and confirm transaction";
        String baseUrl = "https://your-bank.com";
        
        executeAIGeneratedTest(prompt, baseUrl);
    }
    
    // 🔧 Helper method to execute AI-generated tests
    private void executeAIGeneratedTest(String prompt, String baseUrl) {
        logTestInfo("🤖 Generating test from prompt: " + prompt);
        
        try {
            // Generate test cases from your prompt
            List<TestCase> testCases = testGenerator.generateTestCases(prompt, baseUrl);
            
            if (!testCases.isEmpty()) {
                TestCase testCase = testCases.get(0);
                logTestInfo("✅ Generated test case: " + testCase.getName());
                
                // Execute the generated test
                executeGeneratedTestCase(testCase);
                
                logTestPass("🎉 AI-generated test completed successfully!");
            } else {
                logTestInfo("⚠️ No test cases generated, executing manual fallback");
                // You can add manual fallback logic here
            }
            
        } catch (Exception e) {
            logTestFail("❌ Test execution failed: " + e.getMessage());
            throw e;
        }
    }
    
    // Execute the generated test case step by step
    private void executeGeneratedTestCase(TestCase testCase) {
        logTestInfo("📋 Executing test: " + testCase.getDescription());
        
        testCase.getSteps().forEach(step -> {
            logTestInfo("🔄 Step " + step.getStepNumber() + ": " + step.getDescription());
            
            try {
                switch (step.getAction()) {
                    case "NAVIGATE":
                        navigateTo(step.getInputData() != null ? step.getInputData() : testCase.getUrl());
                        break;
                    case "CLICK":
                        if (step.getLocator() != null) {
                            getPage().click(step.getLocator());
                        }
                        break;
                    case "TYPE":
                        if (step.getLocator() != null && step.getInputData() != null) {
                            getPage().fill(step.getLocator(), step.getInputData());
                        }
                        break;
                    case "VERIFY":
                        // Add verification logic
                        if (step.getExpectedResult() != null) {
                            // Simple text verification
                            getPage().waitForSelector("body:has-text('" + step.getExpectedResult() + "')", 
                                new com.microsoft.playwright.Page.WaitForSelectorOptions().setTimeout(5000));
                        }
                        break;
                    case "WAIT":
                        int waitTime = 2000; // default
                        if (step.getInputData() != null) {
                            try {
                                waitTime = Integer.parseInt(step.getInputData()) * 1000;
                            } catch (NumberFormatException ignored) {}
                        }
                        getPage().waitForTimeout(waitTime);
                        break;
                }
                
                logTestPass("✅ Step completed: " + step.getDescription());
                
            } catch (Exception e) {
                logTestFail("❌ Step failed: " + step.getDescription() + " - " + e.getMessage());
                throw new RuntimeException("Test step failed", e);
            }
        });
        
        takeScreenshot("Test completed: " + testCase.getName());
    }
}
