package com.automation.tests.generated;

import com.automation.core.TestBase;
import com.microsoft.playwright.assertions.PlaywrightAssertions;
import org.testng.annotations.Test;

/**
 * Sample Generated Test Class
 * This is an example of an AI-generated test class
 * Generated from prompt: "Navigate to Google and search for Playwright automation"
 */
public class SampleGeneratedTest extends TestBase {
    
    @Test(description = "AI Generated: Navigate to Google and search for Playwright automation", 
          groups = {"generated", "search"})
    public void testGooglePlaywrightSearch() {
        logTestInfo("Starting AI-generated Google search test");
        
        // Step 1: Navigate to Google
        navigateTo("https://www.google.com");
        logTestPass("Navigated to Google");
        
        // Step 2: Accept cookies if present
        try {
            if (getPage().locator("button:has-text('Accept'), button:has-text('I agree')").isVisible()) {
                getPage().click("button:has-text('Accept'), button:has-text('I agree')");
                logTestPass("Accepted cookies");
            }
        } catch (Exception e) {
            logTestInfo("No cookie banner found");
        }
        
        // Step 3: Enter search term
        String searchTerm = "Playwright automation";
        getPage().fill("input[name='q']", searchTerm);
        logTestPass("Entered search term: " + searchTerm);
        
        // Step 4: Submit search
        getPage().press("input[name='q']", "Enter");
        logTestPass("Submitted search");
        
        // Step 5: Wait for results
        getPage().waitForSelector("#search", new com.microsoft.playwright.Page.WaitForSelectorOptions().setTimeout(10000));
        logTestPass("Search results loaded");
        
        // Step 6: Verify results are displayed
        PlaywrightAssertions.assertThat(getPage().locator("#search")).isVisible();
        logTestPass("Search results are visible");
        
        // Step 7: Verify search term appears in results
        PlaywrightAssertions.assertThat(getPage().locator("body")).containsText("Playwright");
        logTestPass("Search results contain 'Playwright'");
        
        // Step 8: Take screenshot
        takeScreenshot("Google Playwright search results");
        
        logTestPass("AI-generated Google search test completed successfully");
    }
    
    @Test(description = "AI Generated: Verify Google search suggestions", 
          groups = {"generated", "search", "suggestions"})
    public void testGoogleSearchSuggestions() {
        logTestInfo("Starting AI-generated search suggestions test");
        
        // Step 1: Navigate to Google
        navigateTo("https://www.google.com");
        logTestPass("Navigated to Google");
        
        // Step 2: Accept cookies if present
        try {
            if (getPage().locator("button:has-text('Accept'), button:has-text('I agree')").isVisible()) {
                getPage().click("button:has-text('Accept'), button:has-text('I agree')");
                logTestPass("Accepted cookies");
            }
        } catch (Exception e) {
            logTestInfo("No cookie banner found");
        }
        
        // Step 3: Start typing search term
        getPage().fill("input[name='q']", "Playwright");
        logTestPass("Started typing search term");
        
        // Step 4: Wait for suggestions
        getPage().waitForTimeout(2000);
        
        // Step 5: Verify suggestions appear
        try {
            if (getPage().locator("ul[role='listbox'], .suggestions, .autocomplete").isVisible()) {
                logTestPass("Search suggestions appeared");
                
                // Step 6: Click on first suggestion if available
                if (getPage().locator("ul[role='listbox'] li").first().isVisible()) {
                    getPage().locator("ul[role='listbox'] li").first().click();
                    logTestPass("Clicked on first suggestion");
                    
                    // Step 7: Verify search was performed
                    getPage().waitForSelector("#search", new com.microsoft.playwright.Page.WaitForSelectorOptions().setTimeout(10000));
                    PlaywrightAssertions.assertThat(getPage().locator("#search")).isVisible();
                    logTestPass("Search performed from suggestion");
                }
            } else {
                logTestInfo("No search suggestions found");
            }
        } catch (Exception e) {
            logTestInfo("Search suggestions not available: " + e.getMessage());
        }
        
        // Step 8: Take screenshot
        takeScreenshot("Google search suggestions test");
        
        logTestPass("AI-generated search suggestions test completed");
    }
}
