package com.automation.runners;

import com.automation.core.TestBase;
import com.automation.generator.TestGenerator;
import com.automation.models.TestCase;
import com.automation.models.TestStep;
import org.testng.annotations.Test;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * File-Based Test Runner
 * Reads test prompts from external files for easy test creation
 */
public class FileBasedTestRunner extends TestBase {
    
    private final TestGenerator testGenerator = new TestGenerator();
    private final String PROMPTS_FILE = "test-prompts.txt";
    
    /**
     * Execute tests from prompts file
     * Users just need to edit test-prompts.txt file
     */
    @Test(description = "Execute Tests from Prompts File", groups = {"file-based", "prompts"})
    public void executeTestsFromFile() {
        
        logTestInfo("📁 Reading test prompts from file: " + PROMPTS_FILE);
        
        List<PromptTestCase> promptTests = readPromptsFromFile();
        
        if (promptTests.isEmpty()) {
            logTestInfo("⚠️ No test prompts found in file");
            return;
        }
        
        logTestInfo("📋 Found " + promptTests.size() + " test prompts");
        
        // Execute each prompt-based test
        for (PromptTestCase promptTest : promptTests) {
            executePromptTest(promptTest);
        }
        
        logTestPass("🎉 All file-based tests completed!");
    }
    
    /**
     * Execute specific test by name from file
     */
    @Test(description = "Execute Specific Test from File", groups = {"file-based", "specific"})
    public void executeSpecificTestFromFile() {
        
        // 🎯 USER: Change this to run specific test by name
        String testNameToRun = "Login Flow Test";
        
        logTestInfo("🎯 Looking for specific test: " + testNameToRun);
        
        List<PromptTestCase> promptTests = readPromptsFromFile();
        
        for (PromptTestCase promptTest : promptTests) {
            if (promptTest.testName.equals(testNameToRun)) {
                logTestInfo("✅ Found test: " + testNameToRun);
                executePromptTest(promptTest);
                return;
            }
        }
        
        logTestFail("❌ Test not found: " + testNameToRun);
    }
    
    /**
     * Execute only Jeevansathi tests from file
     */
    @Test(description = "Execute Jeevansathi Tests from File", groups = {"file-based", "jeevansathi"})
    public void executeJeevansathiTestsFromFile() {
        
        logTestInfo("🎯 Executing only Jeevansathi tests from file");
        
        List<PromptTestCase> promptTests = readPromptsFromFile();
        
        for (PromptTestCase promptTest : promptTests) {
            if (promptTest.baseUrl.contains("jeevansathi")) {
                executePromptTest(promptTest);
            }
        }
        
        logTestPass("🎉 Jeevansathi file-based tests completed!");
    }
    
    // ========================================
    // HELPER METHODS
    // ========================================
    
    /**
     * Read prompts from file
     */
    private List<PromptTestCase> readPromptsFromFile() {
        List<PromptTestCase> promptTests = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(PROMPTS_FILE))) {
            String line;
            int lineNumber = 0;
            
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                line = line.trim();
                
                // Skip comments and empty lines
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }
                
                // Parse line format: TestName | BaseURL | Prompt
                String[] parts = line.split("\\|");
                if (parts.length >= 3) {
                    String testName = parts[0].trim();
                    String baseUrl = parts[1].trim();
                    String prompt = parts[2].trim();
                    
                    promptTests.add(new PromptTestCase(testName, baseUrl, prompt));
                    logTestInfo("📝 Loaded test: " + testName);
                } else {
                    logTestInfo("⚠️ Skipping invalid line " + lineNumber + ": " + line);
                }
            }
            
        } catch (IOException e) {
            logTestFail("❌ Failed to read prompts file: " + e.getMessage());
            logTestInfo("💡 Make sure " + PROMPTS_FILE + " exists in the project root");
        }
        
        return promptTests;
    }
    
    /**
     * Execute a single prompt test
     */
    private void executePromptTest(PromptTestCase promptTest) {
        try {
            logTestInfo("🚀 Executing: " + promptTest.testName);
            logTestInfo("🌐 URL: " + promptTest.baseUrl);
            logTestInfo("📝 Prompt: " + promptTest.prompt);
            
            // Generate test cases from prompt
            List<TestCase> testCases = testGenerator.generateTestCases(promptTest.prompt, promptTest.baseUrl);
            
            if (testCases.isEmpty()) {
                logTestFail("❌ No test cases generated for: " + promptTest.testName);
                return;
            }
            
            // Execute the first generated test case
            TestCase testCase = testCases.get(0);
            logTestInfo("✅ Generated test case with " + testCase.getSteps().size() + " steps");
            
            // Execute each step
            executeGeneratedTestSteps(testCase, promptTest.testName);
            
            logTestPass("✅ Completed: " + promptTest.testName);
            
        } catch (Exception e) {
            logTestFail("❌ Failed: " + promptTest.testName + " - " + e.getMessage());
            takeScreenshot("Failed test: " + promptTest.testName);
        }
    }
    
    /**
     * Execute generated test steps
     */
    private void executeGeneratedTestSteps(TestCase testCase, String testName) {
        logTestInfo("🔄 Executing steps for: " + testName);
        
        for (TestStep step : testCase.getSteps()) {
            logTestInfo("📋 Step " + step.getStepNumber() + ": " + step.getDescription());
            
            try {
                switch (step.getAction().toUpperCase()) {
                    case "NAVIGATE":
                        if (step.getInputData() != null) {
                            navigateTo(step.getInputData());
                            getPage().waitForTimeout(2000);
                        }
                        break;
                        
                    case "CLICK":
                        if (step.getLocator() != null) {
                            // Try multiple strategies for clicking
                            if (!tryClick(step.getLocator())) {
                                logTestInfo("⚠️ Primary locator failed, trying fallback strategies");
                                tryFallbackClick(step.getDescription());
                            }
                        }
                        break;
                        
                    case "TYPE":
                    case "FILL":
                        if (step.getLocator() != null && step.getInputData() != null) {
                            getPage().fill(step.getLocator(), step.getInputData());
                            getPage().waitForTimeout(1000);
                        }
                        break;
                        
                    case "VERIFY":
                    case "ASSERT":
                        if (step.getLocator() != null) {
                            boolean isVisible = getPage().locator(step.getLocator()).isVisible();
                            if (!isVisible) {
                                logTestInfo("⚠️ Verification failed: " + step.getDescription());
                            } else {
                                logTestPass("✅ Verification passed: " + step.getDescription());
                            }
                        }
                        break;
                        
                    case "WAIT":
                        int waitTime = 2000;
                        if (step.getInputData() != null) {
                            try {
                                waitTime = Integer.parseInt(step.getInputData()) * 1000;
                            } catch (NumberFormatException ignored) {}
                        }
                        getPage().waitForTimeout(waitTime);
                        break;
                        
                    default:
                        logTestInfo("⚠️ Unknown action: " + step.getAction());
                        break;
                }
                
                logTestPass("✅ Step completed: " + step.getDescription());
                
            } catch (Exception e) {
                logTestInfo("⚠️ Step had issues: " + step.getDescription() + " - " + e.getMessage());
                // Continue with next step
            }
        }
        
        takeScreenshot("Completed test: " + testName);
    }
    
    /**
     * Try to click element with error handling
     */
    private boolean tryClick(String locator) {
        try {
            if (getPage().locator(locator).isVisible()) {
                getPage().click(locator);
                return true;
            }
        } catch (Exception e) {
            logTestInfo("Click failed for locator: " + locator);
        }
        return false;
    }
    
    /**
     * Try fallback click strategies based on description
     */
    private void tryFallbackClick(String description) {
        String desc = description.toLowerCase();
        
        // Generate fallback locators based on description
        List<String> fallbackLocators = new ArrayList<>();
        
        if (desc.contains("login")) {
            fallbackLocators.add("button:has-text('Login')");
            fallbackLocators.add("a:has-text('Login')");
            fallbackLocators.add("text=/login/i");
        } else if (desc.contains("register")) {
            fallbackLocators.add("button:has-text('Register')");
            fallbackLocators.add("a:has-text('Register')");
            fallbackLocators.add("text=/register/i");
        } else if (desc.contains("submit")) {
            fallbackLocators.add("button[type='submit']");
            fallbackLocators.add("input[type='submit']");
            fallbackLocators.add("button:has-text('Submit')");
        }
        
        // Try each fallback locator
        for (String locator : fallbackLocators) {
            if (tryClick(locator)) {
                logTestPass("✅ Fallback click succeeded: " + locator);
                return;
            }
        }
        
        logTestInfo("⚠️ All fallback click strategies failed");
    }
    
    /**
     * Inner class to hold prompt test data
     */
    private static class PromptTestCase {
        final String testName;
        final String baseUrl;
        final String prompt;
        
        PromptTestCase(String testName, String baseUrl, String prompt) {
            this.testName = testName;
            this.baseUrl = baseUrl;
            this.prompt = prompt;
        }
    }
}
