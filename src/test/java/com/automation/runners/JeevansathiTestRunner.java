package com.automation.runners;

import com.automation.core.TestBase;
import com.automation.logic.JeevansathiLogic;
import com.automation.verification.JeevansathiVerification;
import com.automation.integration.AugmentIntegration;
import org.testng.annotations.Test;

/**
 * Example Test Runner for Jeevansathi
 * Demonstrates how to use Logic and Verification packages with Augment integration
 *
 * This is an EXAMPLE - you can create similar runners for your test scenarios
 */
public class JeevansathiTestRunner extends TestBase {

    private final JeevansathiLogic logic = new JeevansathiLogic();
    private final JeevansathiVerification verification = new JeevansathiVerification();
    private final AugmentIntegration augment = new AugmentIntegration();
    
    @Test(description = "Reset Password Test - Using Logic and Verification Packages", 
          groups = {"jeevansathi", "password-reset"})
    public void resetPasswordTest() {
        
        // === STEP 1: NAVIGATION ===
        logic.navigateToHomepage();
        String pageTitle = logic.getPageTitle();
        boolean pageLoaded = logic.isPageLoaded();
        
        // Verify navigation
        verification.verifyHomepageNavigation(pageTitle);
        verification.verifyPageLoaded(pageLoaded);
        logic.captureScreenshot("Homepage loaded");
        
        // === STEP 2: LOGIN BUTTON CLICK ===
        boolean loginClickResult = logic.clickLoginButton();
        
        // Verify login button click
        verification.verifyLoginButtonClick(loginClickResult);
        logic.captureScreenshot("After clicking login");
        
        // === STEP 3: REGISTER BUTTON VERIFICATION ===
        boolean registerDisplayed = logic.isRegisterButtonDisplayed();
        
        // Verify register button display
        verification.verifyRegisterButtonDisplayed(registerDisplayed);
        logic.captureScreenshot("Register button verification");
        
        // === STEP 4: FORGOT PASSWORD CLICK ===
        boolean forgotPasswordClickResult = logic.clickForgotPasswordLink();
        
        // Verify forgot password click
        verification.verifyForgotPasswordClick(forgotPasswordClickResult);
        logic.captureScreenshot("After clicking forgot password");
        
        // === STEP 5: GET OTP BUTTON VERIFICATION ===
        boolean otpButtonAppearing = logic.isGetOTPButtonAppearing();
        
        // Verify OTP button appearance
        verification.verifyGetOTPButtonAppearing(otpButtonAppearing);
        logic.captureScreenshot("Get OTP button verification");
        
        // === FINAL VERIFICATION ===
        verification.verifyCompleteResetPasswordFlow(
            pageLoaded,
            loginClickResult,
            registerDisplayed,
            forgotPasswordClickResult,
            otpButtonAppearing
        );
    }
    
    @Test(description = "Simple Navigation Test - Logic and Verification Demo", 
          groups = {"jeevansathi", "demo"})
    public void simpleNavigationTest() {
        
        // LOGIC: Perform actions
        logic.navigateToHomepage();
        String pageTitle = logic.getPageTitle();
        boolean pageLoaded = logic.isPageLoaded();
        logic.captureScreenshot("Navigation completed");
        
        // VERIFICATION: Validate results
        verification.verifyHomepageNavigation(pageTitle);
        verification.verifyPageLoaded(pageLoaded);
    }
    
    @Test(description = "Page Elements Verification Test", 
          groups = {"jeevansathi", "verification"})
    public void verifyPageElementsTest() {
        
        // Navigate and verify basic loading
        logic.navigateToHomepage();
        String pageTitle = logic.getPageTitle();
        boolean pageLoaded = logic.isPageLoaded();
        
        verification.verifyHomepageNavigation(pageTitle);
        verification.verifyPageLoaded(pageLoaded);
        logic.captureScreenshot("Homepage verification");
        
        // Check for common elements using logic
        boolean loginElementPresent = logic.isElementPresent("text=/login/i");
        boolean registerElementPresent = logic.isElementPresent("text=/register/i");
        boolean searchElementPresent = logic.isElementPresent("input[type='search'], input[placeholder*='search']");
        
        // Soft verification of elements
        verification.softVerify(loginElementPresent, 
            "Login element found on page", 
            "Login element not found on page");
            
        verification.softVerify(registerElementPresent, 
            "Registration element found on page", 
            "Registration element not found on page");
            
        verification.softVerify(searchElementPresent, 
            "Search functionality found on page", 
            "Search functionality not found on page");
        
        logic.captureScreenshot("Page elements verification completed");
    }

    @Test(description = "Augment Integration Demo",
          groups = {"jeevansathi", "augment"})
    public void augmentIntegrationDemo() {

        logTestInfo("🤖 Demonstrating Augment Integration");

        // Check if Augment is enabled
        if (!augment.isAugmentEnabled()) {
            logTestInfo("⚠️ Augment integration not enabled in config");
            logTestInfo("💡 To enable: Set augment.enabled=true and augment.api.key in config.properties");
        } else {
            logTestInfo("✅ Augment integration is enabled");
        }

        // Test connection (async)
        augment.testConnection().thenAccept(isConnected -> {
            if (isConnected) {
                logTestPass("✅ Successfully connected to Augment API");
            } else {
                logTestInfo("⚠️ Augment API not available - using fallback mechanisms");
            }
        });

        // Demonstrate fallback test generation
        logTestInfo("🔄 Demonstrating fallback test generation");
        executeManualResetPasswordFlow();

        logTestPass("🎉 Augment integration demo completed");
    }

    @Test(description = "Smart Locators Demo",
          groups = {"jeevansathi", "augment", "locators"})
    public void smartLocatorsDemo() {

        logTestInfo("🔍 Demonstrating Smart Locators concept");

        logic.navigateToHomepage();
        verification.verifyPageLoaded(logic.isPageLoaded());

        // Demonstrate fallback locator strategy
        logTestInfo("📍 Using fallback locator strategy for login button");
        boolean loginClicked = logic.clickLoginButton();
        verification.verifyLoginButtonClick(loginClicked);

        logic.captureScreenshot("Smart locators demo completed");
        logTestPass("✅ Smart locators demo completed");
    }

    // Helper method for manual test execution
    private void executeManualResetPasswordFlow() {
        logTestInfo("🔧 Executing manual reset password flow");

        // Navigate
        logic.navigateToHomepage();
        verification.verifyPageLoaded(logic.isPageLoaded());

        // Login
        boolean loginResult = logic.clickLoginButton();
        verification.verifyLoginButtonClick(loginResult);

        // Register check
        boolean registerResult = logic.isRegisterButtonDisplayed();
        verification.verifyRegisterButtonDisplayed(registerResult);

        // Forgot password
        boolean forgotResult = logic.clickForgotPasswordLink();
        verification.verifyForgotPasswordClick(forgotResult);

        // OTP button
        boolean otpResult = logic.isGetOTPButtonAppearing();
        verification.verifyGetOTPButtonAppearing(otpResult);

        logic.captureScreenshot("Manual test flow completed");
        logTestPass("✅ Manual reset password flow completed");
    }
}
