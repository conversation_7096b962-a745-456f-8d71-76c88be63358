package com.automation.runners;

import com.automation.core.TestBase;
import com.automation.generator.TestGenerator;
import com.automation.models.TestCase;
import com.automation.models.TestStep;
import org.testng.annotations.Test;
import org.testng.annotations.DataProvider;

import java.util.List;

/**
 * Prompt-Based Test Runner
 * Users can easily create tests by providing natural language prompts
 */
public class PromptBasedTestRunner extends TestBase {
    
    private final TestGenerator testGenerator = new TestGenerator();
    
    /**
     * EASY WAY 1: Single Prompt Test
     * Users just change the prompt string below
     */
    @Test(description = "Custom Test from User Prompt", groups = {"prompt-based", "custom"})
    public void userCustomTest() {
        
        // 🎯 USER: CHANGE THIS PROMPT TO CREATE YOUR TEST
        String userPrompt = "Navigate to Jeevansathi, click login, verify register button, click forgot password, check OTP button";
        String baseUrl = "https://www.jeevansathi.com/";
        
        logTestInfo("🤖 Generating test from user prompt: " + userPrompt);
        executePromptBasedTest(userPrompt, baseUrl);
    }
    
    /**
     * EASY WAY 2: Multiple Prompts via Data Provider
     * Users can add multiple prompts in the data provider
     */
    @Test(dataProvider = "userPrompts", description = "Multiple Tests from User Prompts", groups = {"prompt-based", "multiple"})
    public void multiplePromptTests(String prompt, String url, String testName) {
        
        logTestInfo("🎯 Executing: " + testName);
        logTestInfo("📝 Prompt: " + prompt);
        executePromptBasedTest(prompt, url);
    }
    
    /**
     * 🎯 USER DATA PROVIDER: ADD YOUR PROMPTS HERE
     * Users can easily add new test scenarios by adding rows
     */
    @DataProvider(name = "userPrompts")
    public Object[][] getUserPrompts() {
        return new Object[][] {
            // Format: {prompt, baseUrl, testName}
            
            // Example 1: Login Flow
            {"Navigate to homepage, click login button, enter username 'testuser' and password 'testpass', click submit, verify dashboard", 
             "https://www.jeevansathi.com/", 
             "Login Flow Test"},
            
            // Example 2: Registration Flow  
            {"Go to registration page, fill name 'John Doe', email '<EMAIL>', password 'password123', click register, verify success message",
             "https://www.jeevansathi.com/",
             "Registration Flow Test"},
            
            // Example 3: Search Flow
            {"Navigate to homepage, enter 'software engineer' in search box, click search, verify results are displayed",
             "https://www.jeevansathi.com/",
             "Search Flow Test"},
            
            // Example 4: Password Reset
            {"Click login, click forgot password, enter email '<EMAIL>', click send OTP, verify OTP page appears",
             "https://www.jeevansathi.com/",
             "Password Reset Test"},
            
            // 🎯 USER: ADD YOUR CUSTOM PROMPTS BELOW
            // {"Your custom prompt here", "https://your-website.com", "Your Test Name"},
            // {"Another prompt", "https://another-site.com", "Another Test"},
        };
    }
    
    /**
     * EASY WAY 3: E-commerce Specific Tests
     * Pre-built prompts for common e-commerce scenarios
     */
    @Test(description = "E-commerce Shopping Flow", groups = {"prompt-based", "ecommerce"})
    public void ecommerceShoppingFlow() {
        
        String prompt = "Navigate to homepage, search for 'laptop', filter by price range $500-1000, " +
                       "add first item to cart, proceed to checkout, fill shipping details, verify order summary";
        String baseUrl = "https://www.amazon.com/"; // User can change this
        
        logTestInfo("🛒 Executing e-commerce shopping flow");
        executePromptBasedTest(prompt, baseUrl);
    }
    
    /**
     * EASY WAY 4: Social Media Tests
     * Pre-built prompts for social media scenarios
     */
    @Test(description = "Social Media Interaction", groups = {"prompt-based", "social"})
    public void socialMediaInteraction() {
        
        String prompt = "Navigate to login page, enter credentials, go to profile, " +
                       "create new post with text 'Hello World', add image, click publish, verify post appears";
        String baseUrl = "https://www.facebook.com/"; // User can change this
        
        logTestInfo("📱 Executing social media interaction");
        executePromptBasedTest(prompt, baseUrl);
    }
    
    /**
     * EASY WAY 5: Banking/Finance Tests
     * Pre-built prompts for banking scenarios
     */
    @Test(description = "Banking Transaction Flow", groups = {"prompt-based", "banking"})
    public void bankingTransactionFlow() {
        
        String prompt = "Login with credentials, navigate to transfer money, select from account, " +
                       "enter recipient details, enter amount $100, verify transaction details, confirm transfer";
        String baseUrl = "https://your-bank-website.com/"; // User can change this
        
        logTestInfo("🏦 Executing banking transaction flow");
        executePromptBasedTest(prompt, baseUrl);
    }
    
    // ========================================
    // HELPER METHODS (Users don't need to modify)
    // ========================================
    
    /**
     * Execute test based on natural language prompt
     */
    private void executePromptBasedTest(String prompt, String baseUrl) {
        try {
            logTestInfo("🤖 Generating test steps from prompt...");
            
            // Generate test cases from prompt
            List<TestCase> testCases = testGenerator.generateTestCases(prompt, baseUrl);
            
            if (testCases.isEmpty()) {
                logTestFail("❌ No test cases generated from prompt");
                return;
            }
            
            // Execute the first generated test case
            TestCase testCase = testCases.get(0);
            logTestInfo("✅ Generated test case: " + testCase.getName());
            logTestInfo("📋 Description: " + testCase.getDescription());
            logTestInfo("🔧 Steps count: " + testCase.getSteps().size());
            
            // Execute each step
            executeGeneratedTestSteps(testCase);
            
            logTestPass("🎉 Prompt-based test completed successfully!");
            
        } catch (Exception e) {
            logTestFail("❌ Prompt-based test failed: " + e.getMessage());
            takeScreenshot("Prompt test failure");
            throw e;
        }
    }
    
    /**
     * Execute generated test steps
     */
    private void executeGeneratedTestSteps(TestCase testCase) {
        logTestInfo("🚀 Executing generated test steps...");
        
        for (TestStep step : testCase.getSteps()) {
            logTestInfo("📋 Step " + step.getStepNumber() + ": " + step.getDescription());
            
            try {
                switch (step.getAction().toUpperCase()) {
                    case "NAVIGATE":
                        if (step.getInputData() != null) {
                            navigateTo(step.getInputData());
                        }
                        break;
                        
                    case "CLICK":
                        if (step.getLocator() != null) {
                            getPage().click(step.getLocator());
                        }
                        break;
                        
                    case "TYPE":
                    case "FILL":
                        if (step.getLocator() != null && step.getInputData() != null) {
                            getPage().fill(step.getLocator(), step.getInputData());
                        }
                        break;
                        
                    case "VERIFY":
                    case "ASSERT":
                        if (step.getLocator() != null) {
                            boolean isVisible = getPage().locator(step.getLocator()).isVisible();
                            if (!isVisible) {
                                logTestFail("❌ Verification failed: " + step.getDescription());
                            } else {
                                logTestPass("✅ Verification passed: " + step.getDescription());
                            }
                        }
                        break;
                        
                    case "WAIT":
                        int waitTime = 2000; // Default 2 seconds
                        if (step.getInputData() != null) {
                            try {
                                waitTime = Integer.parseInt(step.getInputData()) * 1000;
                            } catch (NumberFormatException ignored) {}
                        }
                        getPage().waitForTimeout(waitTime);
                        break;
                        
                    default:
                        logTestInfo("⚠️ Unknown action: " + step.getAction());
                        break;
                }
                
                logTestPass("✅ Step completed: " + step.getDescription());
                
            } catch (Exception e) {
                logTestInfo("⚠️ Step had issues: " + step.getDescription() + " - " + e.getMessage());
                // Continue with next step instead of failing entire test
            }
        }
        
        takeScreenshot("Generated test completed: " + testCase.getName());
    }
}
