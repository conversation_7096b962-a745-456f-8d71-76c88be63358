#!/bin/bash

# Universal Tests Runner
# Processes ALL prompt files and generates/executes tests dynamically

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${MAGENTA}[UNIVERSAL]${NC} $1"
}

print_prompt() {
    echo -e "${CYAN}[PROMPT]${NC} $1"
}

# Parse command line arguments
HEADLESS="false"
BROWSER="chromium"

while [[ $# -gt 0 ]]; do
    case $1 in
        --headless)
            HEADLESS="true"
            shift
            ;;
        --headed)
            HEADLESS="false"
            shift
            ;;
        --browser)
            BROWSER="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Universal Test Runner - Processes ALL prompt files automatically"
            echo ""
            echo "Options:"
            echo "  --headless    Run tests in headless mode (default: false)"
            echo "  --headed      Run tests with visible browser"
            echo "  --browser     Browser to use (chromium, firefox, webkit)"
            echo "  --help        Show this help message"
            echo ""
            echo "How it works:"
            echo "  1. Scans src/test/resources/prompts/pending/ for .txt files"
            echo "  2. Generates test cases from each prompt using AI"
            echo "  3. Executes all generated tests automatically"
            echo "  4. Moves processed prompts to processed/ directory"
            echo "  5. Saves generated test cases as JSON files"
            echo ""
            echo "Examples:"
            echo "  $0                    # Run with visible browser"
            echo "  $0 --headless        # Run in headless mode"
            echo "  $0 --browser firefox  # Run with Firefox"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

echo ""
echo "🌟 Universal Prompt-Based Test Generation & Execution"
echo "====================================================="
print_info "Browser: $BROWSER"
print_info "Headless: $HEADLESS"
print_info "Test Type: Universal (Dynamic Prompt Processing)"
echo ""

# Check for prompt files
PROMPT_DIR="src/test/resources/prompts/pending"
if [ ! -d "$PROMPT_DIR" ]; then
    print_error "Prompt directory not found: $PROMPT_DIR"
    print_info "Creating prompt directory structure..."
    mkdir -p "$PROMPT_DIR"
    mkdir -p "src/test/resources/prompts/processed"
    mkdir -p "src/test/resources/prompts/generated-tests"
fi

# Count prompt files
PROMPT_COUNT=$(find "$PROMPT_DIR" -name "*.txt" 2>/dev/null | wc -l)

print_header "Scanning for prompt files..."
print_info "Prompt directory: $PROMPT_DIR"
print_info "Found $PROMPT_COUNT prompt file(s)"

if [ $PROMPT_COUNT -eq 0 ]; then
    print_warning "No prompt files found!"
    echo ""
    print_info "📝 How to create prompt files:"
    echo "  1. Create .txt files in: $PROMPT_DIR"
    echo "  2. Write your test scenarios in natural language"
    echo "  3. Run this script to generate and execute tests"
    echo ""
    print_info "📋 Example prompt files:"
    echo "  login_module.txt: 'Navigate to website, login with credentials, verify dashboard'"
    echo "  search_module.txt: 'Search for products, filter results, verify search works'"
    echo "  checkout_module.txt: 'Add items to cart, proceed to checkout, complete purchase'"
    echo ""
    print_info "🚀 Quick start:"
    echo "  java -cp target/classes:target/test-classes com.automation.utils.PromptManager"
    exit 0
fi

# Display found prompt files
echo ""
print_prompt "Found prompt files:"
find "$PROMPT_DIR" -name "*.txt" | while read -r file; do
    filename=$(basename "$file")
    content=$(head -c 100 "$file")
    print_prompt "  📄 $filename: $content..."
done

echo ""
print_header "Starting universal test processing..."

# Clean previous reports
print_info "Cleaning previous test reports..."
rm -rf target/htmlReport/* 2>/dev/null || true
rm -rf target/surefire-reports/* 2>/dev/null || true
rm -rf target/allure-results/* 2>/dev/null || true

# Run universal tests
print_info "Running Universal Test Executor..."
echo ""

mvn test \
    -Dtest="Universal Tests" \
    -s settings.xml \
    -Dmaven.exec.skip=true \
    -Dautomation.headless=$HEADLESS \
    -Dautomation.browser=$BROWSER \
    -Dtestng.groups="universal,dynamic,prompt-based"

TEST_EXIT_CODE=$?

echo ""
if [ $TEST_EXIT_CODE -eq 0 ]; then
    print_success "✅ Universal tests completed successfully!"
else
    print_error "❌ Universal tests failed with exit code: $TEST_EXIT_CODE"
fi

# Check results
PROCESSED_COUNT=$(find "src/test/resources/prompts/processed" -name "*.txt" 2>/dev/null | wc -l)
GENERATED_COUNT=$(find "src/test/resources/prompts/generated-tests" -name "*.json" 2>/dev/null | wc -l)

echo ""
print_header "Processing Results:"
print_info "📋 Original prompts: $PROMPT_COUNT"
print_info "✅ Processed prompts: $PROCESSED_COUNT"
print_info "🧪 Generated tests: $GENERATED_COUNT"

# Organize reports
print_info "Organizing test reports..."
./organize-reports.sh

# Display results
echo ""
print_info "📊 Test Results Summary:"
echo "========================="

if [ -f "target/surefire-reports/testng-results.xml" ]; then
    TOTAL=$(grep -o 'total="[0-9]*"' target/surefire-reports/testng-results.xml | grep -o '[0-9]*' || echo "0")
    PASSED=$(grep -o 'passed="[0-9]*"' target/surefire-reports/testng-results.xml | grep -o '[0-9]*' || echo "0")
    FAILED=$(grep -o 'failed="[0-9]*"' target/surefire-reports/testng-results.xml | grep -o '[0-9]*' || echo "0")
    SKIPPED=$(grep -o 'skipped="[0-9]*"' target/surefire-reports/testng-results.xml | grep -o '[0-9]*' || echo "0")
    
    echo "📈 Total Tests: $TOTAL"
    echo "✅ Passed: $PASSED"
    echo "❌ Failed: $FAILED"
    echo "⏭️  Skipped: $SKIPPED"
else
    print_warning "Test results not found"
fi

echo ""
print_info "📋 Available Reports:"
echo "  📊 Main Report:     target/htmlReport/index.html"
echo "  📈 TestNG Report:   target/htmlReport/testng/index.html"
echo "  📸 Screenshots:    target/htmlReport/screenshots/"
echo "  📝 Logs:           target/htmlReport/logs/"

echo ""
print_info "📁 Generated Artifacts:"
echo "  🧪 Test Cases:     src/test/resources/prompts/generated-tests/"
echo "  ✅ Processed:      src/test/resources/prompts/processed/"

echo ""
print_header "🌟 Universal Test Features Demonstrated:"
echo "  🎯 Dynamic prompt processing from .txt files"
echo "  🤖 AI-powered test case generation"
echo "  🔄 Automatic prompt lifecycle management"
echo "  📊 Multi-module test execution"
echo "  🧪 JSON test case artifacts"
echo "  📸 Comprehensive reporting"

echo ""
print_info "🚀 Quick Commands:"
echo "  open target/htmlReport/index.html    # View main report"
echo "  ./run-smoke-tests.sh                 # Run smoke tests"
echo "  ./run-functional-tests.sh            # Run functional tests"
echo "  ./run-complete-suite.sh              # Run complete suite"

echo ""
print_info "📝 Create More Prompts:"
echo "  java -cp target/classes:target/test-classes com.automation.utils.PromptManager"

echo ""
if [ $TEST_EXIT_CODE -eq 0 ]; then
    print_success "🎉 Universal Test Suite completed successfully!"
    print_success "🌟 All prompts processed and tests executed!"
    exit 0
else
    print_error "💥 Universal Test Suite failed!"
    exit $TEST_EXIT_CODE
fi
