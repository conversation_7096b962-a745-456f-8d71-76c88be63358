#!/bin/bash

# Playwright Automation Framework Build Script
# This script builds the project and verifies the build

echo "🚀 Starting Playwright Automation Framework Build..."
echo "=================================================="

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if <PERSON><PERSON> is installed
if ! command -v mvn &> /dev/null; then
    print_error "Maven is not installed. Please install Maven first."
    exit 1
fi

print_success "Maven is installed: $(mvn -version | head -1)"

# Check Java version
if ! command -v java &> /dev/null; then
    print_error "Java is not installed. Please install Java 11 or higher."
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -1 | cut -d'"' -f2 | sed '/^1\./s///' | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 11 ]; then
    print_error "Java 11 or higher is required. Current version: $JAVA_VERSION"
    exit 1
fi

print_success "Java version is compatible: $(java -version 2>&1 | head -1)"

# Clean previous build
print_status "Cleaning previous build..."
mvn clean -s settings.xml -q
if [ $? -eq 0 ]; then
    print_success "Clean completed"
else
    print_error "Clean failed"
    exit 1
fi

# Compile the project
print_status "Compiling source code..."
mvn compile -s settings.xml -q
if [ $? -eq 0 ]; then
    print_success "Source compilation completed"
else
    print_error "Source compilation failed"
    exit 1
fi

# Compile test classes
print_status "Compiling test classes..."
mvn test-compile -s settings.xml -Dmaven.exec.skip=true -q
if [ $? -eq 0 ]; then
    print_success "Test compilation completed"
else
    print_error "Test compilation failed"
    exit 1
fi

# Install Playwright browsers (if not skipping tests)
if [ "$1" != "--skip-browsers" ]; then
    print_status "Installing Playwright browsers..."
    mvn exec:java -e -Dexec.mainClass="com.microsoft.playwright.CLI" -Dexec.args="install" -s settings.xml -q
    if [ $? -eq 0 ]; then
        print_success "Playwright browsers installed"
    else
        print_warning "Playwright browser installation failed (this is optional)"
    fi
fi

# Package the project
print_status "Creating JAR package..."
mvn package -s settings.xml -DskipTests -Dmaven.exec.skip=true -q
if [ $? -eq 0 ]; then
    print_success "JAR package created"
else
    print_error "JAR packaging failed"
    exit 1
fi

# Verify build artifacts
print_status "Verifying build artifacts..."

# Check if JAR file exists
JAR_FILE="target/playwright-automation-framework-1.0.0.jar"
if [ -f "$JAR_FILE" ]; then
    JAR_SIZE=$(ls -lh "$JAR_FILE" | awk '{print $5}')
    print_success "JAR file created: $JAR_FILE ($JAR_SIZE)"
else
    print_error "JAR file not found: $JAR_FILE"
    exit 1
fi

# Check compiled classes
MAIN_CLASSES=$(find target/classes -name "*.class" | wc -l)
TEST_CLASSES=$(find target/test-classes -name "*.class" | wc -l)

print_success "Compiled classes: $MAIN_CLASSES main classes, $TEST_CLASSES test classes"

# Check configuration files
CONFIG_FILES=$(find target/classes -name "*.properties" -o -name "*.xml" | wc -l)
print_success "Configuration files: $CONFIG_FILES files copied to target"

# Display project structure summary
echo ""
echo "📊 Build Summary:"
echo "=================="
echo "✅ Source compilation: SUCCESS"
echo "✅ Test compilation: SUCCESS"
echo "✅ JAR packaging: SUCCESS"
echo "✅ Main classes: $MAIN_CLASSES"
echo "✅ Test classes: $TEST_CLASSES"
echo "✅ Configuration files: $CONFIG_FILES"
echo "✅ JAR size: $JAR_SIZE"

echo ""
echo "🎉 Build completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "=============="
echo "1. Run tests: ./run-tests.sh"
echo "2. Run specific test: mvn test -Dtest=SmokeTests -s settings.xml"
echo "3. Generate reports: mvn allure:serve -s settings.xml"
echo "4. View JAR contents: jar -tf $JAR_FILE"
echo ""
echo "📁 Key Directories:"
echo "=================="
echo "• Source code: src/main/java/"
echo "• Test code: src/test/java/"
echo "• Resources: src/main/resources/"
echo "• Compiled classes: target/classes/"
echo "• Test classes: target/test-classes/"
echo "• JAR file: $JAR_FILE"
echo ""
echo "🔧 Configuration:"
echo "================="
echo "• Default config: src/main/resources/config.properties"
echo "• Dev config: src/main/resources/config-dev.properties"
echo "• Staging config: src/main/resources/config-staging.properties"
echo "• Production config: src/main/resources/config-prod.properties"
echo "• TestNG suite: testng.xml"
echo "• Logging config: src/main/resources/logback.xml"
echo ""

print_success "Playwright Automation Framework is ready to use! 🚀"
