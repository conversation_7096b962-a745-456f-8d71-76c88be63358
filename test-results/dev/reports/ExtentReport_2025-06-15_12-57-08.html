

<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>🎯 Playwright Automation Test Report - White & Magenta Theme</title>
<link rel="apple-touch-icon" href="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@b00a2d0486596e73dd7326beacf352c639623a0e/commons/img/logo.png">
<link rel="shortcut icon" href="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@b00a2d0486596e73dd7326beacf352c639623a0e/commons/img/logo.png">
<link href="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@ce8b10435bcbae260c334c0d0c6b61d2c19b6168/spark/css/spark-style.css" rel="stylesheet" />
<link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@7cc78ce/spark/js/jsontree.js"></script>
<style type="text/css">/* White & Magenta Theme for ExtentReports */body { background: linear-gradient(135deg, #ffffff 0%, #fdf2f8 50%, #f3e8ff 100%) !important; }.navbar-brand, .navbar { background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important; }.nav-pills > li.active > a { background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important; color: white !important; }.panel, .card { background: white !important; border: 2px solid #f3e8ff !important; border-radius: 12px !important; }.panel-heading, .card-header { background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important; color: white !important; }.test-pass { background: linear-gradient(135deg, #10b981 0%, #d946ef 30%) !important; color: white !important; }.test-fail { background: linear-gradient(135deg, #ef4444 0%, #d946ef 30%) !important; color: white !important; }.test-skip { background: linear-gradient(135deg, #f59e0b 0%, #d946ef 30%) !important; color: white !important; }.progress-bar { background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important; }.btn-primary { background: linear-gradient(135deg, #d946ef 0%, #a855f7 100%) !important; border: none !important; }.test-content { font-size: 14px; }.step-details { margin-left: 20px; }.screenshot-container { text-align: center; margin: 10px 0; }.screenshot-container img { max-width: 100%; height: auto; border: 3px solid #e879f9; border-radius: 8px; }</style></head><body class="spa -report standard">
  <div class="app">
    <div class="layout">
<div class="header navbar">
<div class="vheader">
<div class="nav-logo">
<a href="#">
<div class="logo" style="background-image: url('https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@b00a2d0486596e73dd7326beacf352c639623a0e/commons/img/logo.png')"></div>
</a>
</div>
<ul class="nav-left">
<li class="search-box">
<a class="search-toggle" href="#">
<i class="search-icon fa fa-search"></i>
<i class="search-icon-close fa fa-close"></i>
</a>
</li>
<li class="search-input"><input id="search-tests" class="form-control" type="text" placeholder="Search..."></li>
</ul>
<ul class="nav-right">
<li class="m-r-10">
<a href="#"><span class="badge badge-primary">🚀 Test Execution Report</span></a>
</li>
<li class="m-r-10">
<a href="#"><span class="badge badge-primary">2025-06-15 12:57:08</span></a>
</li>
</ul>
</div>
</div><div class="side-nav">
<div class="side-nav-inner">
<ul class="side-nav-menu">
<li class="nav-item dropdown" onclick="toggleView('test-view')">
<a id="nav-test" class="dropdown-toggle" href="#">
<span class="ico"><i class="fa fa-list"></i></span>
</a>
</li>
<li class="nav-item dropdown" onclick="toggleView('exception-view')">
<a id="nav-exception" class="dropdown-toggle" href="#">
<span class="ico"><i class="fa fa-bug"></i></span>
</a>
</li>
<li class="nav-item dropdown" onclick="toggleView('category-view')">
<a id="nav-category" class="dropdown-toggle" href="#">
<span class="ico"><i class="fa fa-tag"></i></span>
</a>
</li>
<li class="nav-item dropdown" onclick="toggleView('device-view')">
<a id="nav-device" class="dropdown-toggle" href="#">
<span class="ico"><i class="fa fa-tablet"></i></span>
</a>
</li>
<li class="nav-item dropdown" onclick="toggleView('author-view')">
<a id="nav-author" class="dropdown-toggle" href="#">
<span class="ico"><i class="fa fa-user"></i></span>
</a>
</li>
<li class="nav-item dropdown" onclick="toggleView('dashboard-view')">
<a id="nav-dashboard" class="dropdown-toggle" href="#">
<span class="ico"><i class="fa fa-bar-chart"></i></span>
</a>
</li>
</ul>
</div>
</div>      <div class="vcontainer">
        <div class="main-content">
<div class="test-wrapper row view test-view">
  <div class="test-list">
    <div class="test-list-tools">
<ul class="tools pull-left">
<li><a href="#"><span class="font-size-14">Tests</span></a></li>
</ul>
<ul class="tools text-right">
<li class="dropdown">
<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-exclamation-circle"></i></a>
<ul id="status-toggle" class="dropdown-menu dropdown-md p-v-0">
<a class="dropdown-item" status="pass" href="#"><span>Pass</span><span class="status success"></span></a>
<a class="dropdown-item" status="fail" href="#"><span>Fail</span><span class="status danger"></span></a>
<div class="dropdown-divider"></div>
<a status="clear" class="dropdown-item" href="#"><span>Clear</span><span class="pull-right"><i class="fa fa-close"></i></span></a>
</ul>
</li>
<li class="dropdown">
<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-user"></i></a>
<ul id="author-toggle" class="dropdown-menu dropdown-md p-v-0">
<a class="dropdown-item" href="#">AutomationTeam</a>
</ul>
</li>
<li class="dropdown">
<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-tag"></i></a>
<ul id="tag-toggle" class="dropdown-menu dropdown-md p-v-0">
<a class="dropdown-item" href="#">functional</a><a class="dropdown-item" href="#">navigation</a><a class="dropdown-item" href="#">verification</a><a class="dropdown-item" href="#">advanced</a><a class="dropdown-item" href="#">smoke</a><a class="dropdown-item" href="#">jeevansathi</a><a class="dropdown-item" href="#">augment</a><a class="dropdown-item" href="#">password-reset</a><a class="dropdown-item" href="#">prompt-based</a>
</ul>
</li>
<li class="dropdown">
<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-tablet"></i></a>
<ul id="device-toggle" class="dropdown-menu dropdown-md p-v-0">
<a class="dropdown-item" href="#">chromium(Headless)</a>
</ul>
</li>
</ul>
</div>    <div class="test-list-wrapper scrollable">
      <ul class="test-list-item">
        <li class="test-item"  status="pass" test-id="1"
          author="AutomationTeam"
          tag="jeevansathi navigation smoke"
          device="chromium(Headless)">
          <div class="test-detail">
            <p class="name">simpleNavigationTest</p>
            <p class="text-sm">
              <span>12:57:10 pm</span> / <span>00:00:06:649</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">simpleNavigationTest</h5>
<span class='badge badge-success'>06.15.2025 12:57:10 pm</span>
<span class='badge badge-danger'>06.15.2025 12:57:16 pm</span>
<span class='badge badge-default'>00:00:06:649</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=1</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">AutomationTeam</span>
<span class="badge badge-pill badge-default">jeevansathi</span>
<span class="badge badge-pill badge-default">navigation</span>
<span class="badge badge-pill badge-default">smoke</span>
<span class="badge badge-pill badge-default">chromium(Headless)</span></div>
<div class="m-t-10 m-l-5">Simple Navigation Test</div>
</div>
</div>    <div class="row mb-3"><div class="col-md-3">
<img data-featherlight='../screenshots/PASSED_simpleNavigationTest_2025-06-15_12-57-16-204.png' src="../screenshots/PASSED_simpleNavigationTest_2025-06-15_12-57-16-204.png">
    
    </div></div>
<div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:10 pm</td>
        <td>
          Test started: simpleNavigationTest
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:10 pm</td>
        <td>
          Test class: com.automation.logic.JeevansathiLogic
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:10 pm</td>
        <td>
          Test description: Simple Navigation Test
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:10 pm</td>
        <td>
          Test groups: navigation, jeevansathi, smoke
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:10 pm</td>
        <td>
          Browser: chromium
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:10 pm</td>
        <td>
          Headless mode: true
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:10 pm</td>
        <td>
          Environment: dev
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:10 pm</td>
        <td>
          Operating System: Mac OS X
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:10 pm</td>
        <td>
          Java Version: 17.0.15
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>12:57:16 pm</td>
        <td>
          Test completed successfully
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>12:57:16 pm</td>
        <td>
          Test Success Screenshot
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:16 pm</td>
        <td>
          Test duration: 5934 ms (5.934 seconds)
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="fail" test-id="2"
          author="AutomationTeam"
          tag="jeevansathi password-reset functional"
          device="chromium(Headless)">
          <div class="test-detail">
            <p class="name">resetPasswordTest</p>
            <p class="text-sm">
              <span>12:57:18 pm</span> / <span>00:00:07:841</span>
              <span class="badge fail-bg log float-right">Fail</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-fail">resetPasswordTest</h5>
<span class='badge badge-success'>06.15.2025 12:57:18 pm</span>
<span class='badge badge-danger'>06.15.2025 12:57:26 pm</span>
<span class='badge badge-default'>00:00:07:841</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=2</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">AutomationTeam</span>
<span class="badge badge-pill badge-default">jeevansathi</span>
<span class="badge badge-pill badge-default">password-reset</span>
<span class="badge badge-pill badge-default">functional</span>
<span class="badge badge-pill badge-default">chromium(Headless)</span></div>
<div class="m-t-10 m-l-5">Reset Password Test - Complete Flow</div>
</div>
</div>    <div class="row mb-3"><div class="col-md-3">
<img data-featherlight='../screenshots/FAILED_resetPasswordTest_2025-06-15_12-57-25-908.png' src="../screenshots/FAILED_resetPasswordTest_2025-06-15_12-57-25-908.png">
    
    </div></div>
<div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:18 pm</td>
        <td>
          Test started: resetPasswordTest
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:18 pm</td>
        <td>
          Test class: com.automation.logic.JeevansathiLogic
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:18 pm</td>
        <td>
          Test description: Reset Password Test - Complete Flow
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:18 pm</td>
        <td>
          Test groups: password-reset, functional, jeevansathi
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:18 pm</td>
        <td>
          Browser: chromium
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:18 pm</td>
        <td>
          Headless mode: true
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:18 pm</td>
        <td>
          Environment: dev
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:18 pm</td>
        <td>
          Operating System: Mac OS X
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:18 pm</td>
        <td>
          Java Version: 17.0.15
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>12:57:25 pm</td>
        <td>
          Test failed: Register button should be displayed on the login page expected [true] but found [false]
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>12:57:25 pm</td>
        <td>
          Stack trace
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>12:57:25 pm</td>
        <td>
          <textarea readonly class="code-block">java.lang.AssertionError: Register button should be displayed on the login page expected [true] but found [false]
	at org.testng.Assert.fail(Assert.java:111)
	at org.testng.Assert.failNotEquals(Assert.java:1578)
	at org.testng.Assert.assertTrue(Assert.java:57)
	at com.automation.verification.JeevansathiVerification.verifyRegisterButtonDisplayed(JeevansathiVerification.java:44)
	at com.automation.logic.JeevansathiLogic.resetPasswordTest(JeevansathiLogic.java:228)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)
	at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:664)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:227)
	at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)
	at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:957)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:200)
	at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
	at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:848)
	at org.testng.TestRunner.run(TestRunner.java:621)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
	at org.testng.SuiteRunner.run(SuiteRunner.java:336)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1280)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1200)
	at org.testng.TestNG.runSuites(TestNG.java:1114)
	at org.testng.TestNG.run(TestNG.java:1082)
	at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:308)
	at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:71)
	at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:113)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
</textarea>
          
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>12:57:26 pm</td>
        <td>
          Test Failure Screenshot
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:26 pm</td>
        <td>
          Test duration: 7360 ms (7.36 seconds)
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="fail" test-id="3"
          author="AutomationTeam"
          tag="jeevansathi verification functional"
          device="chromium(Headless)">
          <div class="test-detail">
            <p class="name">verifyPageElementsTest</p>
            <p class="text-sm">
              <span>12:57:27 pm</span> / <span>00:00:00:061</span>
              <span class="badge fail-bg log float-right">Fail</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-fail">verifyPageElementsTest</h5>
<span class='badge badge-success'>06.15.2025 12:57:27 pm</span>
<span class='badge badge-danger'>06.15.2025 12:57:27 pm</span>
<span class='badge badge-default'>00:00:00:061</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=3</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">AutomationTeam</span>
<span class="badge badge-pill badge-default">jeevansathi</span>
<span class="badge badge-pill badge-default">verification</span>
<span class="badge badge-pill badge-default">functional</span>
<span class="badge badge-pill badge-default">chromium(Headless)</span></div>
<div class="m-t-10 m-l-5">Page Elements Verification</div>
</div>
</div>    <div class="row mb-3"><div class="col-md-3">
<img data-featherlight='../screenshots/FAILED_verifyPageElementsTest_2025-06-15_12-57-27-707.png' src="../screenshots/FAILED_verifyPageElementsTest_2025-06-15_12-57-27-707.png">
    
    </div></div>
<div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:27 pm</td>
        <td>
          Test started: verifyPageElementsTest
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:27 pm</td>
        <td>
          Test class: com.automation.logic.JeevansathiLogic
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:27 pm</td>
        <td>
          Test description: Page Elements Verification
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:27 pm</td>
        <td>
          Test groups: functional, jeevansathi, verification
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:27 pm</td>
        <td>
          Browser: chromium
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:27 pm</td>
        <td>
          Headless mode: true
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:27 pm</td>
        <td>
          Environment: dev
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:27 pm</td>
        <td>
          Operating System: Mac OS X
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:27 pm</td>
        <td>
          Java Version: 17.0.15
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>12:57:27 pm</td>
        <td>
          Test failed: Playwright connection closed
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>12:57:27 pm</td>
        <td>
          Stack trace
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>12:57:27 pm</td>
        <td>
          <textarea readonly class="code-block">com.microsoft.playwright.PlaywrightException: Playwright connection closed
	at com.microsoft.playwright.impl.PipeTransport.send(PipeTransport.java:50)
	at com.microsoft.playwright.impl.Connection.internalSendMessage(Connection.java:167)
	at com.microsoft.playwright.impl.Connection.sendMessageAsync(Connection.java:133)
	at com.microsoft.playwright.impl.Connection.sendMessage(Connection.java:129)
	at com.microsoft.playwright.impl.ChannelOwner.sendMessage(ChannelOwner.java:106)
	at com.microsoft.playwright.impl.FrameImpl.navigateImpl(FrameImpl.java:463)
	at com.microsoft.playwright.impl.PageImpl.lambda$navigate$45(PageImpl.java:836)
	at com.microsoft.playwright.impl.LoggingSupport.withLogging(LoggingSupport.java:47)
	at com.microsoft.playwright.impl.ChannelOwner.withLogging(ChannelOwner.java:89)
	at com.microsoft.playwright.impl.PageImpl.navigate(PageImpl.java:836)
	at com.microsoft.playwright.impl.PageImpl.navigate(PageImpl.java:42)
	at com.microsoft.playwright.Page.navigate(Page.java:5305)
	at com.automation.utils.ActionUtility.navigateTo(ActionUtility.java:24)
	at com.automation.pages.BasePage.navigateTo(BasePage.java:26)
	at com.automation.pages.JeevansathiHomePage.navigateToHomepage(JeevansathiHomePage.java:44)
	at com.automation.logic.JeevansathiLogic.navigateToHomepage(JeevansathiLogic.java:53)
	at com.automation.logic.JeevansathiLogic.verifyPageElementsTest(JeevansathiLogic.java:300)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)
	at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:664)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:227)
	at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)
	at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:957)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:200)
	at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
	at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:848)
	at org.testng.TestRunner.run(TestRunner.java:621)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
	at org.testng.SuiteRunner.run(SuiteRunner.java:336)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1280)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1200)
	at org.testng.TestNG.runSuites(TestNG.java:1114)
	at org.testng.TestNG.run(TestNG.java:1082)
	at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:308)
	at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:71)
	at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:113)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
</textarea>
          
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>12:57:27 pm</td>
        <td>
          Test Failure Screenshot
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:27 pm</td>
        <td>
          Test duration: 3 ms (0.003 seconds)
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="4"
          author="AutomationTeam"
          tag="jeevansathi advanced prompt-based"
          device="chromium(Headless)">
          <div class="test-detail">
            <p class="name">promptBasedTest</p>
            <p class="text-sm">
              <span>12:57:28 pm</span> / <span>00:00:00:128</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">promptBasedTest</h5>
<span class='badge badge-success'>06.15.2025 12:57:28 pm</span>
<span class='badge badge-danger'>06.15.2025 12:57:28 pm</span>
<span class='badge badge-default'>00:00:00:128</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=4</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">AutomationTeam</span>
<span class="badge badge-pill badge-default">jeevansathi</span>
<span class="badge badge-pill badge-default">advanced</span>
<span class="badge badge-pill badge-default">prompt-based</span>
<span class="badge badge-pill badge-default">chromium(Headless)</span></div>
<div class="m-t-10 m-l-5">Prompt-Based Test Generation</div>
</div>
</div>    <div class="row mb-3"><div class="col-md-3">
<img data-featherlight='../screenshots/PASSED_promptBasedTest_2025-06-15_12-57-28-287.png' src="../screenshots/PASSED_promptBasedTest_2025-06-15_12-57-28-287.png">
    
    </div></div>
<div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Test started: promptBasedTest
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Test class: com.automation.logic.JeevansathiLogic
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Test description: Prompt-Based Test Generation
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Test groups: jeevansathi, prompt-based, advanced
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Browser: chromium
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Headless mode: true
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Environment: dev
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Operating System: Mac OS X
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Java Version: 17.0.15
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>12:57:28 pm</td>
        <td>
          Test completed successfully
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>12:57:28 pm</td>
        <td>
          Test Success Screenshot
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Test duration: 71 ms (0.071 seconds)
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="fail" test-id="5"
          author="AutomationTeam"
          tag="jeevansathi advanced augment"
          device="chromium(Headless)">
          <div class="test-detail">
            <p class="name">augmentIntegrationDemo</p>
            <p class="text-sm">
              <span>12:57:28 pm</span> / <span>00:00:00:072</span>
              <span class="badge fail-bg log float-right">Fail</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-fail">augmentIntegrationDemo</h5>
<span class='badge badge-success'>06.15.2025 12:57:28 pm</span>
<span class='badge badge-danger'>06.15.2025 12:57:28 pm</span>
<span class='badge badge-default'>00:00:00:072</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=5</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">AutomationTeam</span>
<span class="badge badge-pill badge-default">jeevansathi</span>
<span class="badge badge-pill badge-default">advanced</span>
<span class="badge badge-pill badge-default">augment</span>
<span class="badge badge-pill badge-default">chromium(Headless)</span></div>
<div class="m-t-10 m-l-5">Augment Integration Demo</div>
</div>
</div>    <div class="row mb-3"><div class="col-md-3">
<img data-featherlight='../screenshots/FAILED_augmentIntegrationDemo_2025-06-15_12-57-28-752.png' src="../screenshots/FAILED_augmentIntegrationDemo_2025-06-15_12-57-28-752.png">
    
    </div></div>
<div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Test started: augmentIntegrationDemo
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Test class: com.automation.logic.JeevansathiLogic
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Test description: Augment Integration Demo
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Test groups: augment, jeevansathi, advanced
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Browser: chromium
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Headless mode: true
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Environment: dev
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Operating System: Mac OS X
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Java Version: 17.0.15
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>12:57:28 pm</td>
        <td>
          Test failed: Playwright connection closed
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>12:57:28 pm</td>
        <td>
          Stack trace
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>12:57:28 pm</td>
        <td>
          <textarea readonly class="code-block">com.microsoft.playwright.PlaywrightException: Playwright connection closed
	at com.microsoft.playwright.impl.PipeTransport.send(PipeTransport.java:50)
	at com.microsoft.playwright.impl.Connection.internalSendMessage(Connection.java:167)
	at com.microsoft.playwright.impl.Connection.sendMessageAsync(Connection.java:133)
	at com.microsoft.playwright.impl.Connection.sendMessage(Connection.java:129)
	at com.microsoft.playwright.impl.ChannelOwner.sendMessage(ChannelOwner.java:106)
	at com.microsoft.playwright.impl.FrameImpl.navigateImpl(FrameImpl.java:463)
	at com.microsoft.playwright.impl.PageImpl.lambda$navigate$45(PageImpl.java:836)
	at com.microsoft.playwright.impl.LoggingSupport.withLogging(LoggingSupport.java:47)
	at com.microsoft.playwright.impl.ChannelOwner.withLogging(ChannelOwner.java:89)
	at com.microsoft.playwright.impl.PageImpl.navigate(PageImpl.java:836)
	at com.microsoft.playwright.impl.PageImpl.navigate(PageImpl.java:42)
	at com.microsoft.playwright.Page.navigate(Page.java:5305)
	at com.automation.utils.ActionUtility.navigateTo(ActionUtility.java:24)
	at com.automation.pages.BasePage.navigateTo(BasePage.java:26)
	at com.automation.pages.JeevansathiHomePage.navigateToHomepage(JeevansathiHomePage.java:44)
	at com.automation.logic.JeevansathiLogic.navigateToHomepage(JeevansathiLogic.java:53)
	at com.automation.logic.JeevansathiLogic.augmentIntegrationDemo(JeevansathiLogic.java:405)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)
	at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:664)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:227)
	at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)
	at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:957)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:200)
	at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
	at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.testng.TestRunner.privateRun(TestRunner.java:848)
	at org.testng.TestRunner.run(TestRunner.java:621)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
	at org.testng.SuiteRunner.run(SuiteRunner.java:336)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1280)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1200)
	at org.testng.TestNG.runSuites(TestNG.java:1114)
	at org.testng.TestNG.run(TestNG.java:1082)
	at org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:308)
	at org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:71)
	at org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:113)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
</textarea>
          
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>12:57:28 pm</td>
        <td>
          Test Failure Screenshot
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log info-bg">Info</span></td>
        <td>12:57:28 pm</td>
        <td>
          Test duration: 2 ms (0.002 seconds)
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
      </ul>
    </div>
  </div>
<div class="test-content scrollable">
<div class="test-content-tools">
<ul><li><a class="back-to-test" href="#"><i class="fa fa-arrow-left"></i></a></li></ul>
</div>
<div class="test-content-detail"><div class="detail-body"></div></div>
</div></div>
<div class="test-wrapper row view exception-view attributes-view">
<div class="test-list">
<div class="test-list-tools">
<ul class="tools pull-left"><li><a href=""><span class="font-size-14">Exception</span></a></li></ul>
<ul class="tools text-right"><li><a href="#"><span class="badge badge-primary">2</span></a></li></ul>
</div>
<div class="test-list-wrapper scrollable">
<ul class="test-list-item">
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log badge-danger'>2</span>
</span>
<p class="name">com.microsoft.playwright.PlaywrightException</p>
<p class="duration text-sm">2 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>com.microsoft.playwright.PlaywrightException</h4>
<span status="fail" class='badge log badge-danger'>2 failed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:27 pm</td>
<td>
<a href="#" class="linked" test-id='3' id='3'>verifyPageElementsTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:28 pm</td>
<td>
<a href="#" class="linked" test-id='5' id='5'>augmentIntegrationDemo</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log badge-danger'>1</span>
</span>
<p class="name">java.lang.AssertionError</p>
<p class="duration text-sm">1 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>java.lang.AssertionError</h4>
<span status="fail" class='badge log badge-danger'>1 failed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:18 pm</td>
<td>
<a href="#" class="linked" test-id='2' id='2'>resetPasswordTest</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
</ul>
</div>
</div>
<div class="test-content scrollable">
<div class="test-content-detail">
<div class="detail-body"></div>
</div>
</div>
</div><div class="test-wrapper row view category-view attributes-view">
<div class="test-list">
<div class="test-list-tools">
<ul class="tools pull-left"><li><a href=""><span class="font-size-14">Category</span></a></li></ul>
<ul class="tools text-right"><li><a href="#"><span class="badge badge-primary">9</span></a></li></ul>
</div>
<div class="test-list-wrapper scrollable">
<ul class="test-list-item">
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log badge-danger'>2</span>
</span>
<p class="name">functional</p>
<p class="duration text-sm">2 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>functional</h4>
<span status="fail" class='badge log badge-danger'>2 failed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:18 pm</td>
<td>
<a href="#" class="linked" test-id='2' id='2'>resetPasswordTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:27 pm</td>
<td>
<a href="#" class="linked" test-id='3' id='3'>verifyPageElementsTest</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log pass-bg'>1</span>
</span>
<p class="name">navigation</p>
<p class="duration text-sm">1 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>navigation</h4>
<span status="pass" class='badge log pass-bg'>1 passed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>12:57:10 pm</td>
<td>
<a href="#" class="linked" test-id='1' id='1'>simpleNavigationTest</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log badge-danger'>1</span>
</span>
<p class="name">verification</p>
<p class="duration text-sm">1 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>verification</h4>
<span status="fail" class='badge log badge-danger'>1 failed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:27 pm</td>
<td>
<a href="#" class="linked" test-id='3' id='3'>verifyPageElementsTest</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log pass-bg'>1</span>
<span class='badge log badge-danger'>1</span>
</span>
<p class="name">advanced</p>
<p class="duration text-sm">2 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>advanced</h4>
<span status="pass" class='badge log pass-bg'>1 passed</span>
<span status="fail" class='badge log badge-danger'>1 failed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>12:57:28 pm</td>
<td>
<a href="#" class="linked" test-id='4' id='4'>promptBasedTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:28 pm</td>
<td>
<a href="#" class="linked" test-id='5' id='5'>augmentIntegrationDemo</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log pass-bg'>1</span>
</span>
<p class="name">smoke</p>
<p class="duration text-sm">1 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>smoke</h4>
<span status="pass" class='badge log pass-bg'>1 passed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>12:57:10 pm</td>
<td>
<a href="#" class="linked" test-id='1' id='1'>simpleNavigationTest</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log pass-bg'>2</span>
<span class='badge log badge-danger'>3</span>
</span>
<p class="name">jeevansathi</p>
<p class="duration text-sm">5 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>jeevansathi</h4>
<span status="pass" class='badge log pass-bg'>2 passed</span>
<span status="fail" class='badge log badge-danger'>3 failed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>12:57:10 pm</td>
<td>
<a href="#" class="linked" test-id='1' id='1'>simpleNavigationTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:18 pm</td>
<td>
<a href="#" class="linked" test-id='2' id='2'>resetPasswordTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:27 pm</td>
<td>
<a href="#" class="linked" test-id='3' id='3'>verifyPageElementsTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>12:57:28 pm</td>
<td>
<a href="#" class="linked" test-id='4' id='4'>promptBasedTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:28 pm</td>
<td>
<a href="#" class="linked" test-id='5' id='5'>augmentIntegrationDemo</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log badge-danger'>1</span>
</span>
<p class="name">augment</p>
<p class="duration text-sm">1 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>augment</h4>
<span status="fail" class='badge log badge-danger'>1 failed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:28 pm</td>
<td>
<a href="#" class="linked" test-id='5' id='5'>augmentIntegrationDemo</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log badge-danger'>1</span>
</span>
<p class="name">password-reset</p>
<p class="duration text-sm">1 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>password-reset</h4>
<span status="fail" class='badge log badge-danger'>1 failed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:18 pm</td>
<td>
<a href="#" class="linked" test-id='2' id='2'>resetPasswordTest</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log pass-bg'>1</span>
</span>
<p class="name">prompt-based</p>
<p class="duration text-sm">1 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>prompt-based</h4>
<span status="pass" class='badge log pass-bg'>1 passed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>12:57:28 pm</td>
<td>
<a href="#" class="linked" test-id='4' id='4'>promptBasedTest</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
</ul>
</div>
</div>
<div class="test-content scrollable">
<div class="test-content-detail">
<div class="detail-body"></div>
</div>
</div>
</div><div class="test-wrapper row view device-view attributes-view">
<div class="test-list">
<div class="test-list-tools">
<ul class="tools pull-left"><li><a href=""><span class="font-size-14">Device</span></a></li></ul>
<ul class="tools text-right"><li><a href="#"><span class="badge badge-primary">1</span></a></li></ul>
</div>
<div class="test-list-wrapper scrollable">
<ul class="test-list-item">
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log pass-bg'>2</span>
<span class='badge log badge-danger'>3</span>
</span>
<p class="name">chromium(Headless)</p>
<p class="duration text-sm">5 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>chromium(Headless)</h4>
<span status="pass" class='badge log pass-bg'>2 passed</span>
<span status="fail" class='badge log badge-danger'>3 failed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>12:57:10 pm</td>
<td>
<a href="#" class="linked" test-id='1' id='1'>simpleNavigationTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:18 pm</td>
<td>
<a href="#" class="linked" test-id='2' id='2'>resetPasswordTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:27 pm</td>
<td>
<a href="#" class="linked" test-id='3' id='3'>verifyPageElementsTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>12:57:28 pm</td>
<td>
<a href="#" class="linked" test-id='4' id='4'>promptBasedTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:28 pm</td>
<td>
<a href="#" class="linked" test-id='5' id='5'>augmentIntegrationDemo</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
</ul>
</div>
</div>
<div class="test-content scrollable">
<div class="test-content-detail">
<div class="detail-body"></div>
</div>
</div>
</div><div class="test-wrapper row view author-view attributes-view">
<div class="test-list">
<div class="test-list-tools">
<ul class="tools pull-left"><li><a href=""><span class="font-size-14">Author</span></a></li></ul>
<ul class="tools text-right"><li><a href="#"><span class="badge badge-primary">1</span></a></li></ul>
</div>
<div class="test-list-wrapper scrollable">
<ul class="test-list-item">
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log pass-bg'>2</span>
<span class='badge log badge-danger'>3</span>
</span>
<p class="name">AutomationTeam</p>
<p class="duration text-sm">5 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>AutomationTeam</h4>
<span status="pass" class='badge log pass-bg'>2 passed</span>
<span status="fail" class='badge log badge-danger'>3 failed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>12:57:10 pm</td>
<td>
<a href="#" class="linked" test-id='1' id='1'>simpleNavigationTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:18 pm</td>
<td>
<a href="#" class="linked" test-id='2' id='2'>resetPasswordTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:27 pm</td>
<td>
<a href="#" class="linked" test-id='3' id='3'>verifyPageElementsTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>12:57:28 pm</td>
<td>
<a href="#" class="linked" test-id='4' id='4'>promptBasedTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>12:57:28 pm</td>
<td>
<a href="#" class="linked" test-id='5' id='5'>augmentIntegrationDemo</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
</ul>
</div>
</div>
<div class="test-content scrollable">
<div class="test-content-detail">
<div class="detail-body"></div>
</div>
</div>
</div><div class="container-fluid p-4 view dashboard-view">
<div class="row">
<div class="col-md-3">
<div class="card"><div class="card-body">
<p class="m-b-0">Started</p>
<h3>2025-06-15 12:57:08</h3>
</div></div>
</div>
<div class="col-md-3">
<div class="card"><div class="card-body">
<p class="m-b-0">Ended</p>
<h3>2025-06-15 12:57:29</h3>
</div></div>
</div>
<div class="col-md-3">
<div class="card"><div class="card-body">
<p class="m-b-0 text-pass">Tests Passed</p>
<h3>2</h3>
</div></div>
</div>
<div class="col-md-3">
<div class="card"><div class="card-body">
<p class="m-b-0 text-fail">Tests Failed</p>
<h3>3</h3>
</div></div>
</div>
</div>
<div class="row">
<div class="col-md-6">
<div class="card">
<div class="card-header">
<h6 class="card-title">Tests</h6>
</div>
<div class="card-body">
<div class="">
<canvas id='parent-analysis' width='115' height='90'></canvas>
</div>
</div>
<div class="card-footer">
<div><small data-tooltip='40%'>
<b>2</b> tests passed
</small>
</div>
<div>
<small data-tooltip='60%'><b>3</b> tests failed,
<b>0</b> skipped, <b data-tooltip='0%'>0</b> others
</small>
</div>
</div>
</div>
</div>
<div class="col-md-6">
<div class="card">
<div class="card-header">
<h6 class="card-title">Log events</h6>
</div>
<div class="card-body">
<div class="">
<canvas id='events-analysis' width='115' height='90'></canvas>
</div>
</div>
<div class="card-footer">
<div><small data-tooltip='6%'><b>4</b> events passed</small></div>
<div>
<small data-tooltip='18%'><b>12</b> events failed,
<b data-tooltip='%'>50</b> others
</small>
</div>
</div>
</div>
</div>
</div>
<div class="row"><div class="col-md-12">
<div class="card"><div class="card-header"><p>Timeline</p></div>
<div class="card-body pt-0"><div>
<canvas id="timeline" height="120"></canvas>
</div></div>
</div>
</div></div>
<script>
var timeline = {
"simpleNavigationTest":6.649,"resetPasswordTest":7.841,"verifyPageElementsTest":0.061,"promptBasedTest":0.128,"augmentIntegrationDemo":0.072
};
</script>
<div class="row">
<div class="col-lg-6 col-md-12 author-container">
<div class="card">
<div class="card-header"><p>Author</p></div>
<div class="card-body pb-0 pt-0"><table class="table table-sm table-bordered">
<thead><tr class="bg-gray"><th>Name</th><th>Passed</th><th>Failed</th><th>Skipped</th><th>Others</th><th>Passed %</th></tr></thead>
<tbody>
<tr>
<td>AutomationTeam</td>
<td>2</td>
<td>3</td>
<td>0</td>
<td>0</td>
<td>40%</td>
</tr>
</tbody>
</table></div>
</div>
</div>
<div class="col-lg-6 col-md-12 category-container">
<div class="card">
<div class="card-header"><p>Tags</p></div>
<div class="card-body pb-0 pt-0"><table class="table table-sm table-bordered">
<thead><tr class="bg-gray"><th>Name</th><th>Passed</th><th>Failed</th><th>Skipped</th><th>Others</th><th>Passed %</th></tr></thead><tbody>
<tr>
<td>functional</td>
<td>0</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>0%</td>
</tr>
<tr>
<td>navigation</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0</td>
<td>100%</td>
</tr>
<tr>
<td>verification</td>
<td>0</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0%</td>
</tr>
<tr>
<td>advanced</td>
<td>1</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>50%</td>
</tr>
<tr>
<td>smoke</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0</td>
<td>100%</td>
</tr>
<tr>
<td>jeevansathi</td>
<td>2</td>
<td>3</td>
<td>0</td>
<td>0</td>
<td>40%</td>
</tr>
<tr>
<td>augment</td>
<td>0</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0%</td>
</tr>
<tr>
<td>password-reset</td>
<td>0</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0%</td>
</tr>
<tr>
<td>prompt-based</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0</td>
<td>100%</td>
</tr>
</tbody>
</table></div>
</div>
</div>
<div class="col-lg-6 col-md-12 device-container">
<div class="card">
<div class="card-header"><p>Device</p></div>
<div class="card-body pb-0 pt-0"><table class="table table-sm table-bordered">
<thead><tr class="bg-gray"><th>Name</th><th>Passed</th><th>Failed</th><th>Skipped</th><th>Others</th><th>Passed %</th></tr></thead>
<tbody>
<tr>
<td>chromium(Headless)</td>
<td>2</td>
<td>3</td>
<td>0</td>
<td>0</td>
<td>40%</td>
</tr>
</tbody>
</table></div>
</div>
</div>
<div class="col-lg-6 col-md-12 sysenv-container">
<div class="card">
<div class="card-header"><p>System/Environment</p></div>
<div class="card-body pb-0 pt-0"><table class="table table-sm table-bordered">
<thead><tr class="bg-gray"><th>Name</th><th>Value</th></tr></thead>
<tbody>
<tr>
<td>Operating System</td>
<td>Mac OS X</td>
</tr>
<tr>
<td>OS Version</td>
<td>15.2</td>
</tr>
<tr>
<td>Java Version</td>
<td>17.0.15</td>
</tr>
<tr>
<td>User</td>
<td>aniketmaharaj</td>
</tr>
<tr>
<td>Environment</td>
<td>dev</td>
</tr>
<tr>
<td>Browser</td>
<td>chromium</td>
</tr>
<tr>
<td>Base URL</td>
<td>https://dev.example.com</td>
</tr>
<tr>
<td>Headless Mode</td>
<td>false</td>
</tr>
<tr>
<td>Parallel Execution</td>
<td>false</td>
</tr>
<tr>
<td>Thread Count</td>
<td>1</td>
</tr>
</tbody>
</table></div>
</div>
</div>
</div>
</div>
<script>
var statusGroup = {
parentCount: 5,
passParent: 2,
failParent: 3,
warningParent: 0,
skipParent: 0,
childCount: 5,
passChild: 0,
failChild: 0,
warningChild: 0,
skipChild: 0,
infoChild: 0,
grandChildCount: 5,
passGrandChild: 0,
failGrandChild: 0,
warningGrandChild: 0,
skipGrandChild: 0,
infoGrandChild: 0,
eventsCount: 5,
passEvents: 4,
failEvents: 12,
warningEvents: 0,
skipEvents: 0,
infoEvents: 50
};
</script>        </div>
      </div>
    </div>
  </div>
<script src="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@ce8b10435bcbae260c334c0d0c6b61d2c19b6168/spark/js/spark-script.js"></script>
<script type="text/javascript"></script></body>
</html>