# 🤖 AI Test Generation Prompts
# Add your automation requirements here in plain English

# E-commerce Testing
Navigate to Amazon, search for "laptop", filter by price range $500-$1000, sort by customer rating, and add the first item to cart

# Social Media Testing  
Login to Twitter, compose a new tweet with text "Testing automation framework", add hashtag #automation, and post the tweet

# Banking/Finance Testing
Go to online banking portal, login with credentials, check account balance, transfer $50 to savings account, and verify transaction

# Travel Booking Testing
Visit Expedia, search for flights from New York to London for next month, filter by non-stop flights, select cheapest option, and proceed to booking

# Food Delivery Testing
Open Uber Eats, enter delivery address, search for "pizza", select a restaurant, add items to cart, and proceed to checkout

# Educational Platform Testing
Navigate to Coursera, search for "Python programming", filter by beginner level, enroll in a free course, and verify enrollment

# Job Portal Testing
Go to LinkedIn Jobs, search for "Software Engineer" in "San Francisco", apply filters for remote work, and save the first job posting

# News Website Testing
Visit BBC News, navigate to Technology section, read the first article, share it on social media, and verify sharing worked

# Streaming Service Testing
Login to Netflix, search for "action movies", add a movie to watchlist, start playing a trailer, and verify video plays

# Real Estate Testing
Open Zillow, search for homes in "Seattle", filter by price range and bedrooms, save a property to favorites, and contact agent

# Healthcare Testing
Navigate to health portal, login with patient credentials, book an appointment with doctor, select available time slot, and confirm booking

# Government Services Testing
Visit DMV website, start driver license renewal process, fill required information, upload documents, and submit application

# Retail Shopping Testing
Go to Target website, search for "home decor", filter by room type, add multiple items to cart, apply coupon code, and proceed to checkout

# Cryptocurrency Testing
Open Coinbase, check Bitcoin price, set up price alert, view transaction history, and verify account balance

# Cloud Storage Testing
Login to Google Drive, create new folder, upload a document, share folder with specific email, and verify sharing permissions
