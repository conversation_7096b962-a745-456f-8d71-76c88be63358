# 🤖 Test Prompts File - Easy Test Creation
# Users can add their test prompts here in simple text format
# Format: TestName | BaseURL | Prompt Description

# Jeevansathi Tests
Login Flow Test | https://www.jeevansathi.com/ | Navigate to homepage, click login button, enter username 'testuser' and password 'testpass', click submit, verify dashboard appears

Registration Test | https://www.jeevansathi.com/ | Go to registration page, fill name '<PERSON>', email '<EMAIL>', password 'password123', click register button, verify success message

Password Reset | https://www.jeevansathi.com/ | Click login, click forgot password link, enter email '<EMAIL>', click send OTP button, verify OTP input page appears


# E-commerce Testing
Shopping Cart | https://www.amazon.com/ | Search for 'laptop', filter by price $500-1000, add first item to cart, proceed to checkout, verify cart contents

Product Search | https://www.flipkart.com/ | Navigate to homepage, enter 'smartphone' in search box, click search button, verify search results are displayed

# Social Media Testing
Create Post | https://www.facebook.com/ | Login with credentials, go to home feed, create new post with text 'Hello World', click publish, verify post appears

Profile Update | https://www.linkedin.com/ | Login, go to profile page, click edit profile, update job title to 'Senior Developer', save changes, verify update

# Banking/Finance Testing
Money Transfer | https://your-bank.com/ | Login with credentials, navigate to transfer section, select from account, enter recipient details, enter amount $100, confirm transfer

Account Balance | https://your-bank.com/ | Login, go to accounts overview, click on savings account, verify balance is displayed, check recent transactions

# 🎯 USER SECTION: Add Your Custom Tests Below
# Format: Your Test Name | https://your-website.com/ | Your detailed test description

# Example Custom Tests:
Contact Form | https://your-company.com/ | Navigate to contact page, fill name 'Test User', email '<EMAIL>', message 'Hello', click submit, verify thank you message

Newsletter Signup | https://your-blog.com/ | Scroll to footer, enter email '<EMAIL>' in subscription box, click subscribe, verify confirmation message

# Travel Booking Testing
Visit Expedia, search for flights from New York to London for next month, filter by non-stop flights, select cheapest option, and proceed to booking

# Food Delivery Testing
Open Uber Eats, enter delivery address, search for "pizza", select a restaurant, add items to cart, and proceed to checkout

# Educational Platform Testing
Navigate to Coursera, search for "Python programming", filter by beginner level, enroll in a free course, and verify enrollment

# Job Portal Testing
Go to LinkedIn Jobs, search for "Software Engineer" in "San Francisco", apply filters for remote work, and save the first job posting

# News Website Testing
Visit BBC News, navigate to Technology section, read the first article, share it on social media, and verify sharing worked

# Streaming Service Testing
Login to Netflix, search for "action movies", add a movie to watchlist, start playing a trailer, and verify video plays

# Real Estate Testing
Open Zillow, search for homes in "Seattle", filter by price range and bedrooms, save a property to favorites, and contact agent

# Healthcare Testing
Navigate to health portal, login with patient credentials, book an appointment with doctor, select available time slot, and confirm booking

# Government Services Testing
Visit DMV website, start driver license renewal process, fill required information, upload documents, and submit application

# Retail Shopping Testing
Go to Target website, search for "home decor", filter by room type, add multiple items to cart, apply coupon code, and proceed to checkout

# Cryptocurrency Testing
Open Coinbase, check Bitcoin price, set up price alert, view transaction history, and verify account balance

# Cloud Storage Testing
Login to Google Drive, create new folder, upload a document, share folder with specific email, and verify sharing permissions
